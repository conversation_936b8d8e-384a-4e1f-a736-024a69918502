<?php

return [
    // 统计配置
    'static_config' => [
        // 统计数据保存表名
        'table_name' => 'org_tube_static',
        
        // 统计时间间隔（秒）
        'interval' => 3600, // 1小时
        
        // 批处理大小
        'batch_size' => 100,
        
        // 是否启用日志记录
        'enable_log' => true,
        
        // 日志级别
        'log_level' => 'info',
        
        // 统计数据保留天数
        'retention_days' => 30,
    ],
    
    // 统计字段映射
    'static_fields' => [
        'play_count' => '播放人数',
        'finish_count' => '完播人数', 
        'answer_count' => '答题人数',
        'correct_count' => '答对人数',
    ],
    
    // 数据库字段配置
    'db_fields' => [
        // 播放人数统计条件
        'play_condition' => [],

        // 完播人数统计条件
        'finish_condition' => ['is_finish' => 1],

        // 答题人数统计条件
        'answer_condition' => ['answer_id', '>', 0],

        // 答对人数统计条件
        'correct_condition' => ['is_correct' => 1],
    ],

    // 命令执行配置
    'command_config' => [
        // 是否允许并发执行
        'allow_concurrent' => false,

        // 锁文件路径
        'lock_file' => './runtime/dostatic.lock',

        // 最大执行时间（秒）
        'max_execution_time' => 3600,

        // 内存限制
        'memory_limit' => '256M',
    ],

    // 通知配置
    'notification' => [
        // 是否启用通知
        'enabled' => false,

        // 通知方式：email, webhook
        'methods' => ['email'],

        // 邮件通知配置
        'email' => [
            'to' => '<EMAIL>',
            'subject' => '群管统计任务执行结果',
        ],

        // Webhook通知配置
        'webhook' => [
            'url' => '',
            'method' => 'POST',
        ],
    ],
];
