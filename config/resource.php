<?php

return [
    // 阿里云视频点播配置
    'aliyun_vod' => [
        'region_id' => 'cn-beijing', //SDK默认服务接入点为cn-shanghai（上海）
        'access_key_id' => 'LTAI5t8pgBWv6c3SbchqT6X5', // 替换为您的AccessKeyId
        'access_key_secret' => '******************************', // 替换为您的AccessKeySecret
        'aliyun_user_id' => '1265345719626219',//阿里云的账号ID
        'license_key' => 'SPnqKo9VEFTJZZciWb5701de7d26d4328bef8b95626b0c426',//阿里云的License Key
    ],

    // 微信商户：多连主体
    'wxsh_duolian' => [
        // 必要配置
        'app_id'             => 'wx4ac69bbd444ada49',//开发者ID(AppID)
        'app_secret'         => '70ecc8572c6e4ba004327d4104b76e07',//开发者秘钥（AppSecret）
        'mch_id'             => '**********',
        'key'                => '5j34v2TzE7ueHk2Qt7aVWtkeThNfsj2G',   // API v2 密钥 (注意: 是v2密钥 是v2密钥 是v2密钥)

        // 如需使用敏感接口（如退款、发送红包等）需要配置 API 证书路径(登录商户平台下载 API 证书)
        'cert_path'          => __DIR__ . '/wxpay/apiclient_cert.pem', // XXX: 绝对路径！！！！
        'key_path'           => __DIR__ . '/wxpay/apiclient_key.pem',      // XXX: 绝对路径！！！！

        'notify_url'         => 'https://toa.duolian.info/Tool/wxNotify',     // 你也可以在下单时单独设置来想覆盖它
    ],


];
