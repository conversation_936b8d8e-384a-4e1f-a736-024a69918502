# DoStatic 升级说明

## 升级概述

本次升级将原有的 `org_tube_static` 表结构升级为 `date_statistics` 表结构，以支持更完善的多维度统计功能。

## 主要变更

### 1. 数据表结构变更

**原表结构 (`org_tube_static`)**:
- 简单的日期字段 (`stat_date`)
- 基础的群管和经销商信息
- 4个统计字段

**新表结构 (`date_statistics`)**:
- 分离的时间字段 (`year`, `month`, `day`, `hour`)
- 完整的层级信息 (商户、公众号、经销商、群管)
- 相同的4个统计字段，字段名略有调整

### 2. 字段名称变更

| 原字段名 | 新字段名 | 说明 |
|----------|----------|------|
| `play_count` | `watch_count` | 播放人数 → 观看人数 |
| `finish_count` | `finish_count` | 完播人数（无变化） |
| `answer_count` | `answer_count` | 答题人数（无变化） |
| `correct_count` | `correct_count` | 答对人数（无变化） |

### 3. 新增功能

- **按小时统计**: 支持更精细的时间维度统计
- **多层级信息**: 包含完整的商户、公众号、经销商、群管信息
- **经销商过滤**: 支持按经销商ID进行统计
- **更灵活的时间处理**: 年月日小时分离存储

## 升级步骤

### 1. 备份现有数据（如果存在）

```sql
-- 备份现有统计数据（如果存在 org_tube_static 表）
CREATE TABLE ksd_org_tube_static_backup AS SELECT * FROM ksd_org_tube_static;
```

### 2. 创建新表

执行 `database_static_table.sql` 中的 SQL 语句：

```sql
-- 创建新的统计表
CREATE TABLE IF NOT EXISTS `ksd_date_statistics` (
  -- ... 完整的表结构见 database_static_table.sql
);
```

### 3. 数据迁移（如果需要）

如果您有现有的统计数据需要迁移，可以使用以下 SQL：

```sql
-- 将旧数据迁移到新表（示例）
INSERT INTO ksd_date_statistics (
    year, month, day, hour,
    agency_id, agency_name,
    tube_id, tube_name,
    watch_count, finish_count, answer_count, correct_count,
    create_time, update_time
)
SELECT 
    YEAR(stat_date) as year,
    MONTH(stat_date) as month,
    DAY(stat_date) as day,
    0 as hour,  -- 旧数据按天统计，小时设为0
    agency_id,
    '' as agency_name,  -- 需要后续补充
    tube_id,
    tube_name,
    play_count as watch_count,
    finish_count,
    answer_count,
    correct_count,
    create_time,
    update_time
FROM ksd_org_tube_static_backup;
```

### 4. 更新配置文件

确保 `config/static.php` 中的表名配置正确：

```php
'table_name' => 'date_statistics',
```

### 5. 测试新功能

运行测试脚本验证升级是否成功：

```bash
php test_dostatic.php
```

## 配置变更

### 新增配置项

在 `config/static.php` 中新增了以下配置：

```php
// 是否按小时统计（false为按天统计）
'hourly_statistics' => false,

// 默认小时值（当按天统计时使用）
'default_hour' => 0,

// 数据保留天数（从30天增加到90天）
'retention_days' => 90,
```

## 命令变更

### 新增选项

- `--hourly` (`-h`): 按小时统计
- `--agency_id` (`-a`): 指定经销商ID

### 使用示例

```bash
# 按小时统计
php think dostatic -h

# 指定经销商统计
php think dostatic -a 123

# 组合使用
php think dostatic -t 456 -a 123 -d 2024-01-15 -h
```

## 兼容性说明

### 向后兼容

- 原有的基本命令仍然可用
- 统计逻辑保持一致
- 数据精度没有降低

### 不兼容变更

- 表结构完全变更，无法直接使用旧表
- 字段名称有部分调整
- 时间存储方式改变

## 性能优化

### 索引优化

新表结构包含更多索引以提升查询性能：

- 复合唯一索引：`(year, month, day, hour, tube_id)`
- 单字段索引：`org_id`, `agency_id`, `tube_id`, `gzh_id`
- 时间索引：`(year, month, day)`

### 查询优化

- 时间范围查询更高效
- 支持多维度聚合查询
- 更好的数据分区支持

## 故障排除

### 常见问题

1. **表不存在错误**
   - 确保执行了建表 SQL
   - 检查表名前缀配置

2. **字段不存在错误**
   - 确认使用的是新的字段名
   - 检查代码中的字段引用

3. **数据不一致**
   - 重新运行统计命令
   - 检查时间范围设置

### 回滚方案

如果升级出现问题，可以：

1. 恢复备份的旧表
2. 修改配置文件指向旧表
3. 使用旧版本的命令代码

## 后续计划

- 添加更多统计维度
- 支持实时统计
- 增加数据可视化功能
- 优化大数据量处理性能
