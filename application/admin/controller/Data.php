<?php

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use app\admin\Service\Helper;

class Data extends Base
{
    /**
     * * 课程统计
     */
    public function course()
    {
        //统计org_user_watch中的课程
        $org_id = $this->org_id;
        $size = request()->param('size');
        $filter['course_id'] = input('course_id', '');
        $filter['wxgzh_id'] = input('wxgzh_id', 0);
        $filter['start_date'] = input('start_date', '');
        $filter['end_date'] = input('end_date', '');

        if(!empty($filter['start_date']) && !empty($filter['end_date']) && 
           strtotime($filter['start_date']) > strtotime($filter['end_date'])) {
            $this->error('开始时间不能大于结束时间');
        }
        //默认查询当日
        if(empty($filter['start_date']) && empty($filter['end_date'])) {
            $filter['start_date'] = date('Y-m-d');
            $filter['end_date'] = date('Y-m-d');
        }

        $where = [];
        $where[] = ['t1.org_id', '=', $org_id];

        if (!empty($filter['course_id'])) {
            $where[] = ['i.course_id', '=', $filter['course_id']];
        }
        if (!empty($filter['wxgzh_id'])) {
            $wxgzh_id = $filter['wxgzh_id'];
            $where[] = ['p.wxgzh_id', '=', $wxgzh_id];
        }
        if (!empty($filter['start_date'])) {
            $start_time = strtotime($filter['start_date']);
            $where[] = ['t1.create_time', '>=', $start_time];
        }
        if (!empty($filter['end_date'])) {
            $end_time = strtotime("+1 day", strtotime($filter['end_date']));
            $where[] = ['t1.create_time', '<', $end_time];
        }

        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_user_watch')->alias('t1')
            ->leftJoin('org_period_item i', 'i.item_id = t1.item_id')
            ->leftJoin('org_course c', 'i.course_id = c.course_id')
            ->leftjoin('org_period p', 'i.period_id = p.period_id')
            ->where($where)
            ->field('c.course_name, c.course_id, COUNT(t1.id) AS watch_count, 
                     COUNT(DISTINCT t1.user_id) AS user_count,
                     SUM(CASE WHEN t1.is_finish = 1 THEN 1 ELSE 0 END ) AS finish_count,
                     SUM(CASE WHEN t1.answer_id > 0 THEN 1 ELSE 0 END) AS question_count,
                     SUM(CASE WHEN t1.is_correct = 1 THEN 1 ELSE 0 END) AS correct_count')
            ->group('i.course_id')
            ->paginate($pagesize);
        // dump(Db::getLastSql());
        
        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }
    /**
     * 营期统计period
     */
    public function period()
    {
        $org_id = $this->org_id;
        $size = request()->param('size');
        $filter['wxgzh_id'] = input('wxgzh_id', 0);
        $filter['start_date'] = input('start_date', '');
        $filter['end_date'] = input('end_date', '');
        $filter['group_id'] = input('group_id', 0);
        $filter['period_id'] = input('period_id', 0);

        if(!empty($filter['start_date']) && !empty($filter['end_date']) && 
           strtotime($filter['start_date']) > strtotime($filter['end_date'])) {
            $this->error('开始时间不能大于结束时间');
        }
        //默认查询当日
        if(empty($filter['start_date']) && empty($filter['end_date'])) {
            $filter['start_date'] = date('Y-m-d');
            $filter['end_date'] = date('Y-m-d');
        }

        $where = [];
        $where[] = ['t1.org_id', '=', $org_id];

        if (!empty($filter['group_id'])) {
            $where[] = ['pg.group_id', '=', $filter['group_id']];
        }
        if (!empty($filter['period_id'])) {
            $where[] = ['p.period_id', '=', $filter['period_id']];
        }
        if (!empty($filter['wxgzh_id'])) {
            $wxgzh_id = $filter['wxgzh_id'];
            $where[] = ['p.wxgzh_id', '=', $wxgzh_id];
        }
        if (!empty($filter['start_date'])) {
            $start_time = strtotime($filter['start_date']);
            $where[] = ['t1.create_time', '>=', $start_time];
        }
        if (!empty($filter['end_date'])) {
            $end_time = strtotime("+1 day", strtotime($filter['end_date']));
            $where[] = ['t1.create_time', '<', $end_time];
        }

        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_user_watch')->alias('t1')
            ->leftJoin('org_period_item i', 'i.item_id = t1.item_id')
            ->leftJoin('org_period p', 'i.period_id = p.period_id')
            ->leftJoin('org_course c', 'i.course_id = c.course_id')
            ->leftjoin('org_period_group pg', 'pg.group_id = p.group_id')
            ->leftjoin('org_agency a', 'a.agency_id = t1.agency_id')
            ->where($where)
            ->field('pg.group_name,ANY_VALUE(p.period_name) AS period_name,ANY_VALUE(a.agency_name) AS agency_name,
                     COUNT(t1.id) AS watch_count, 
                     COUNT(DISTINCT t1.user_id) AS user_count,
                     SUM(CASE WHEN t1.is_finish = 1 THEN 1 ELSE 0 END ) AS finish_count,
                     SUM(CASE WHEN t1.answer_id > 0 THEN 1 ELSE 0 END) AS question_count,
                     SUM(CASE WHEN t1.is_correct = 1 THEN 1 ELSE 0 END) AS correct_count')
            ->group('pg.group_id')
            ->paginate($pagesize);
        // dump($rows);
        
        // 获取分页显示
        $page = $rows->render();

        //获取训练营列表
        $period_group = db('org_period_group')
            ->where(['org_id' => $org_id])
            ->field('group_id, group_name')
            ->select();
        $this->assign('period_group', $period_group);

        //获取营期名称
        $pwhere[] = array(
            ['org_id', '=', $org_id],
            ['del', '=', 0],
        );
        if(!empty($filter['group_id'])) {
            $pwhere[] = ['group_id', '=', $filter['group_id']];
        }
        $periods = db('org_period')
            ->where($pwhere)
            ->field('period_id, period_name')
            ->select();
        $this->assign('periods', $periods);

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }
}
