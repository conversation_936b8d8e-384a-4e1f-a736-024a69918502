<?php

/**
 * @file            Comman.php
 * @version         1.0
 * @description     不需要登录，不需要验证权限的通用controller
 */

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\Exception;
use think\facade\Request;
use EasyWeChat\Factory;
use think\facade\Log;

class Tool extends Controller
{
    /**
     * 获取二维码信息
     */
    public function qrcode()
    {
        $params = input();
        $content = $params['content'];
        $size = $params['size'];
        require_once "./../vendor/qrcode/phpqrcode.php";
        //纠错级别， 纠错级别越高，生成图片会越大
        //L水平    7%的字码可被修正
        //M水平    15%的字码可被修正
        //Q水平    25%的字码可被修正
        //H水平    30%的字码可被修正
        $level = "L";
        //图片每个黑点的像素。
        //生成图片 第二个参数：是否保存成文件 如需要保存文件，第二个参数改为文件名即可,如：'qrcode.png'
        \QRcode::png($content, false, $level, $size);
    }

    /**
     * 微信的支付回传
     */
    public function wxNotify(){
        Log::notice('WxNotify通知日志:'); // 记录一条通知日志信息
        // $xml = file_get_contents('php://input');
        // $xmlObj = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        // Log::notice($xmlObj); 

        $config = config('resource.wxsh_duolian');
        $app = Factory::payment($config);
        $response = $app->handlePaidNotify(function($message, $fail){
            Log::notice($message); 
            // 使用通知里的 "微信支付订单号" 或者 "商户订单号" 去自己的数据库找到订单
            $order_id = wx_out_trade_no($message['out_trade_no']);
            //不管支付状态如何，先记录回传的信息
            //此处有个大bug，如果在：handlePaidNotify外面定义了$time=time(),然后里面引用$time就会出现值是Null的情况，插入报错
            $logAttr = array(
                'order_id' => $order_id,
                'out_trade_no' => $message['out_trade_no'],
                'pay_id' => 1,//微信
                'transaction_id' => $message['transaction_id'],
                'amount' => $message['cash_fee'] / 100,
                'code' => $message['result_code'],
                'create_time' => time(),
                'response' => json_encode($message),
            );
            /////////////// Log::notice($logAttr); 通过记录log才查出的问题，太坑/////////////
            $id = db("notify_paylog")->insertGetId($logAttr);
            
            //接下来处理返回信息
            $order = db('order')->where('order_id', $order_id)->find();
            Log::notice($order); 
            if (!$order || $order['order_status'] == 5) { // 如果订单不存在 或者 订单已经支付过了
                // return true; // 告诉微信，我已经处理完了，订单没找到，别再通知我了
                $this->_ok_response();
                exit;
            }
            $resp = array();
            ///////////// <- 建议在这里调用微信的【订单查询】接口查一下该笔订单的情况，确认是已经支付 /////////////
            if ($message['return_code'] === 'SUCCESS') { // return_code 表示通信状态，不代表支付状态
                
                // 用户是否支付成功
                if ($message['result_code'] === 'SUCCESS') {
                    $resp['pay_time'] = time(); // 更新支付时间为当前时间
                    $resp['order_status'] = 5;
                    $this->_processOrder($order, $resp);
                // 用户支付失败
                } elseif ($message['result_code'] === 'FAIL') {
                    $resp['order_status'] = 3;
                    // $this->_processOrder($order, $resp);
                }
            } else {
                return $fail('通信失败，请稍后再通知我');
            }
            // return true; // 返回处理完成
            $this->_ok_response();
            exit;
        });
        $response->send(); // return $response;
    }

    /**
     * 支付成功以后处理订单的逻辑
     */
    private function _processOrder($order, $resp){
        $time = time();
        Db::startTrans();
        try {
            $resp['update_time'] = $time;
            $amount = $order['amount'];
            //1.更新订单状态
            Db::name('order')->where('order_id',$order['order_id'])->update($resp);

            //2.更新余额
            $billData = [
                'order_id' => $order['order_id'],
                'org_id' => $order['org_id'],
                'agency_id' => $order['agency_id'],
                'type' => 1,
                'obj' => $order['obj'],
                'operator_id' => $order['operator_id'],
                'operator_name' => $order['operator_name'],
                'create_time' => $time,
                'remark' => '充值已到账，余额增加【'.$amount.'】元',
            ];
            Log::notice($billData); 
            if($order['obj'] == 1){//情况一：增加org余额
                $org = Db::name('org')->where('org_id',$order['org_id'])->find();
                $billData['balance_before'] = $org['balance'];
                $billData['amount'] = $amount;
                //增加org的余额
                $sql = "update ksd_org set balance = balance + $amount where org_id = ".$order['org_id'];
                $n = Db::execute($sql);
                //此次有两种方式：获取最新的org表数据来得到余额，也可直接用：$billData['balance_before'] + $amount
                $new_org = Db::name('org')->where('org_id',$order['org_id'])->find();
                $billData['balance_after'] = $new_org['balance'];
                //后续可以增加一个监控：
                //org_balance_bill表的：balance_before + amount 是否等于 balance_after
            }elseif($order['obj'] == 3){//情况二：增加agency余额
                $agency = Db::name('org_agency')->where('agency_id',$order['agency_id'])->find();
                $billData['balance_before'] = $agency['balance'];
                $billData['amount'] = $amount;
                //增加agency的余额
                $sql = "update ksd_org_agency set balance = balance + $amount where agency_id = ".$order['agency_id'];
                $n = Db::execute($sql);
                $new_agency = Db::name('org_agency')->where('agency_id',$order['agency_id'])->find();
                $billData['balance_after'] = $new_agency['balance'];
                //后续也可以增加一个监控
            }
            $id = Db::name('org_balance_bill')->insertGetId($billData);
            //3.记录log
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $result = array("code"=>400,"msg"=>$e->getMessage());
            return $result;
            exit;
        }
    }

    /**
     * 支付成功后的返回信息
     */
    public function _ok_response(){
        echo '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
    }

}