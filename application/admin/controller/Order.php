<?php

namespace app\admin\controller;
use EasyWeChat\Factory;
use think\Controller;
use think\Db;

class Order extends Base
{
    /**
     * 订单列表
     */
    public function index()
    {
        $org_id = $this->org_id;
        $size = request()->param('size');
        $filter['keyword'] = input('keyword', '');
        $where = [];
        $where[] = ['t.order_type', '=', 1];
        $where[] = ['t.org_id', '=', $org_id];
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t.order_id', '=', "$keyword"];
        }
        //***Start***：如果登录账号是：经销商
        $where = new_agency_where($where, 't');
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('order')->alias('t')
            ->leftJoin("org o","t.org_id = o.org_id")
            ->leftJoin("org_agency a","t.agency_id = a.agency_id")
            ->where($where)
            ->field("t.*, a.agency_name, o.name org_name")
            ->order('t.create_time desc')
            ->paginate($pagesize);
        // echo DB::getLastSql();exit;
        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        
        return $this->fetch();
    }
    
    /**
     * 去支付
     */
    public function doPay(){
        $params = input();
        $time = time();
        $order_id = $params['order_id'];
        $order = db('order')->where('order_id', $order_id)->find();
        if (empty($order) ) {
            $this->error('信息有误，请重新操作！', url('Index/index'));
        }
        if($order['order_status'] == 5){
            $this->success('订单支付成功!!!', url('Balance/index'));
        }
        //第二步：展示付款二维码
        $config = config('resource.wxsh_duolian');
        $app = Factory::payment($config);
        $resp = $app->order->unify([
            'body' => '充值中心-会员充值',
            'out_trade_no' => wx_out_trade_no($order['order_id']),
            'total_fee' => $order['order_amount'] * 100,
            'notify_url' => $config['notify_url'], // 支付结果通知网址，如果不设置则会使用配置里的默认地址
            'trade_type' => 'NATIVE', // 交易类型：扫码支付不需要openid,JSAPI支付必须有openid
        ]);
        // var_dump(wx_out_trade_no($order['order_id']), $config, $resp);
        $this->assign('resp', $resp);
        $this->assign('order', $order);
        return $this->fetch('balance/pay');
    }

    /**
     * 关闭订单
     */
    public function close(){
        $params = input();
        $time = time();
        $order_id = $params['order_id'];
        $order = db('order')->where('order_id', $order_id)->find();
        if (empty($order) or $order['order_status'] != 1) {
            $this->error('信息有误，请重新操作！', url('Balance/index'));
        }
        $data = [
            'order_id' => $order_id,
            'order_status' => 4,
            'update_time' => $time,
        ];
        if (db('order')->update($data) === false) {
            $this->error('更新失败');
        }
        $this->success('关闭成功!!!', url('Order/index'));
    }

    /**
     * 红包列表
     */
    public function redpackage()
    {
        $org_id = $this->org_id;
        $size = request()->param('size');
        $filter['keyword'] = input('keyword', '');
        $where = [];
        $where[] = ['t.order_type', '=', 3];
        $where[] = ['t.org_id', '=', $org_id];
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t.order_id', '=', "$keyword"];
        }
        //***Start***：如果登录账号是：经销商
        $where = new_agency_where($where, 't');
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('order')->alias('t')
            ->leftJoin("org_agency a","t.agency_id = a.agency_id")
            ->leftJoin("org_agency_tube b","t.tube_id = b.tube_id")
            ->leftJoin("org_wxuser u","t.user_id = u.user_id")
            ->leftJoin("org_wxgzh g","u.wxgzh_id = g.id")
            ->leftJoin("org_red_rule r","t.redrule_id = r.id")
            ->where($where)
            ->field("t.*, a.agency_name, b.tube_name, u.nickname, g.name as gzh_name, r.name rule_name")
            ->order('t.create_time desc')
            ->paginate($pagesize);
        // echo DB::getLastSql();exit;
        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        
        return $this->fetch();
    }

}
