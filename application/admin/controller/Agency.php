<?php

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;

class Agency extends Base
{
    /**
     * * 经销商
     */
    public function index()
    {
        $org_id = $this->org_id;

        $size = request()->param('size');
        $filter['status'] = input('status', 9);
        $filter['wxgzh_id'] = input('wxgzh_id', '');
        $filter['type'] = input('type', '');
        $filter['gone_id'] = input('gone_id', 0);
        $filter['gtwo_id'] = input('gtwo_id', 0);
        $filter['agency_id'] = input('agency_id', 0);
        $where = [];
        $where[] = ['t1.org_id', '=', $org_id];
        $where[] = ['t1.status', '=', $filter['status']];
        
        if (!empty($filter['wxgzh_id'])) {
            $where[] = ['t1.wxgzh_id', '=', $filter['wxgzh_id']];
        }
        if (!empty($filter['type'])) {
            $where[] = ['t1.type', '=', $filter['type']];
        }
        if (!empty($filter['gone_id'])) {
            $where[] = ['g1.parent_id', '=', $filter['gone_id']];
            $this->assign('list_gtwo', getAgencyGroupList($filter['gone_id']));
        }
        if (!empty($filter['gtwo_id'])) {
            $where[] = ['t1.group_id', '=', $filter['gtwo_id']];
        }
        if (!empty($filter['agency_id'])) {
            $where[] = ['t1.agency_id', '=', $filter['agency_id']];
        }
        //***Start***：如果登录账号是：经销商
        $where = new_agency_where($where, 't1');
        $agency_ids = [];
        if(login_user_is_agency()){
            $str_ids = get_user_agency_ids();
            if(!empty($str_ids)){
                $agency_ids = explode(',', $str_ids);
            }
        }
        //***END***
        //审核通过的总数
        $where_9 = $this->_getWhere(9, $agency_ids);
        $count_9 = db('org_agency')
            ->where($where_9)
            ->count();
        $this->assign('count_9', $count_9);
        //待审核的总数
        $where_1 = $this->_getWhere(1, $agency_ids);
        $count_1 = db('org_agency')
            ->where($where_1)
            ->count();
        $this->assign('count_1', $count_1);
        //禁用的总数
        $where_4 = $this->_getWhere(4, $agency_ids);
        $count_4 = db('org_agency')
            ->where($where_4)
            ->count();
        $this->assign('count_4', $count_4);
        //审核拒绝的总数
        $where_5 = $this->_getWhere(5, $agency_ids);
        $count_5 = db('org_agency')
            ->where($where_5)
            ->count();
        $this->assign('count_5', $count_5);

        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_agency')->alias('t1')
            ->leftJoin("org_wxgzh t2","t1.wxgzh_id = t2.id")
            ->leftJoin("org_wxuser t3","t3.user_id = t1.wx_user_id")
            ->leftJoin("org_agency_group g1","t1.group_id = g1.id")
            ->where($where)
            ->field('t1.*,t2.name as gzh_name,t3.last_visit')
            ->order('t1.agency_id desc')
            ->paginate($pagesize);
        //echo DB::getLastSql();exit;
        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    public function audit()
    {
        $org_id = $this->org_id;
        $Model = db('org_agency');
        $time = time();
        $data = array();
        $status = input('status', 0);
        $agency_id = input('agency_id', 0);
        if(empty($agency_id)){
            $this->error('参数错误');
        }
        if(!in_array($status, [9,5,4])){
            $this->error('参数错误');
        }
        $data['status'] = $status;
        $data['update_time'] = $time;
        //更新agency
        if($Model->where('agency_id', $agency_id)->update($data) === false){
            $this->error('审核失败');
        }
        //如果审核通过，需要创建默认的红包规则，此处还需进一步待讨论后确定，2025-6-5 by wenchao
        if($status == 9){//审核通过
            // $rules = db('org_red_cate')->where('status', 1)->select();
            // foreach ($rules as $v) {
            //     $list[] = array(
            //     );
            // }
            $redData = [
                'name' => '答题奖励红包',
                'org_id' => $org_id,
                'agency_id' => $agency_id,
                'agency_name' => '',
                'cate_id' => 1,
                'cate_name' => '答题奖励',
                'red_type' => 1,
                'red_amount' => 0.3,
                'is_default' => 1,
                'status' => 1,
                'create_time' => $time,
                'update_time' => $time,
            ];
            Db::name('org_red_rule')->insertGetId($redData);
        }
        
        //同步更新wx_user状态
        $wx_user_id = db('org_agency')->where('agency_id', $agency_id)->value('wx_user_id');
        if($wx_user_id){
            db('org_wxuser')->where('user_id', $wx_user_id)->update($data);
        }
        $this->success('审核成功');
    }

    /**
     * 经销商解除注册
    */
    public function delete()
    {
        $params = input();
        if (isset($params['agency_id']) && !empty($params['agency_id'])) {
            $map[] = ['agency_id', '=', $params['agency_id']];
            $map[] = ['org_id', '=', $this->org_id];
            //更新经销商表del，更新wxuser表type为公海,清除wxuser表agency_id，tube_id
            $res = db('org_agency')->where($map)->update(['del' => 1]);
            if($res){
                $wx_user_id = db('org_agency')->where('agency_id', $params['agency_id'])->value('wx_user_id');
                db('org_wxuser')->where('user_id', $wx_user_id)->update(['type' => 4, 'agency_id' => 0, 'tube_id' => 0, 'update_time' => time()]);
                $this->success("保存成功", url('index'));
            }else{
                $this->error('解除注册失败');
            }
        } else {
            $this->error('信息有误，请重新操作！', url('index'));
        }
    }

    private function _getWhere($status, $agency_ids){
        $where = [];
        $where[] = ['org_id', '=', $this->org_id];
        $where[] = ['status', '=', $status];
        if(!empty($agency_ids)){
            $where[] = ['agency_id', 'in', $agency_ids];
        }
        return $where;
    }
}
