<?php
/**
 * @file            Balance.php
 * @version         1.0
 * @date            Fri, 23 Feb 2018 11:12:07 GMT
 * @description     经销商余额
 */
namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use app\admin\Service\Helper;

class Balance extends Base
{

    public function index()
    {
        // for ($i=0; $i < 1000; $i++) { 
        //     $id = getUUIDPro('CZ');
        //     echo $i.":".$id."<br>";
        // }
        $org_id = $this->org_id;
        $size = request()->param('size');
        $filter['keyword'] = input('keyword', '');
        $where = [];
        $where[] = ['t.org_id', '=', $org_id];
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['t.name|t2.name', 'like', "%$keyword%"];
        }
        //***Start***：如果登录账号是：经销商
        $where = new_agency_where($where, 't');
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_agency')->alias('t')
            ->leftJoin("org_wxgzh w","t.wxgzh_id = w.id")
            ->where($where)
            ->field("t.*, w.name gzh_name")
            ->order('t.org_id desc')
            ->paginate($pagesize);
        // echo DB::getLastSql();exit;
        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        
        return $this->fetch();
    }

    /**
     * 充值
     */
    public function recharge(){
        $params = input();
        $org_id = $this->org_id;
        $data = array();
        $time = time();
        $action = "BalanceRecharge";
        //如果是传入agency_id，给经销商充值，如果是没有传入给商户充值
        if (isset($params['agency_id']) && !empty($params['agency_id'])) {
            $row = db('org_agency t')
                ->leftJoin("org_wxgzh w","t.wxgzh_id = w.id")
                ->field("t.*, w.name gzh_name")
                ->where('agency_id', $params['agency_id'])->find();
            if (empty($row) or $row['org_id'] != $this->org_id) {
                $this->error('信息有误，请重新操作！', url('index'));
            }
            $model = [
                'name' => base64_decode($row['agency_name']),
                'agency_id' => $row['agency_id'],
            ];
            $this->assign('model', $model);
        }else{
            $row = db('org t')->where('org_id', $org_id)->find();
            $model = [
                'name' => $row['name'],
            ];
            $this->assign('model', $model);
        }

        if (Request::isPost()) {
            // 检查页面提交的与Session储存的Token是否一致，防止重复提交
            if(!empty($_REQUEST['form_token'])){
                Helper::checkFormToken($action);
            }

            $data['order_id'] = getUUIDPro('CZ');
            $data['org_id'] = $org_id;
            $data['order_status'] = 1;//待支付
            $data['order_type'] = 1;//充值
            $data['agency_id'] = input('agency_id', 0);
            $data['obj'] = input('pay_obj', 0);
            $data['amount'] = input('amount', 0);
            $data['service_fee'] = round(0.01 * $data['amount'], 2);
            $data['order_amount'] = $data['amount'] + $data['service_fee'];
            $data['operator_id'] = session('admin.id');
            $data['operator_name'] = session('admin.name');
            $data['create_time'] = $time;
            $data['update_time'] = $time;
            //第一步：先创建订单
            if (!db('order')->insert($data)) {
                $this->error('创建订单失败');
            }
            //第二步：跳转到支付页面
            $this->redirect('Order/doPay', ['order_id'=>$data['order_id']]);
        } else {
            $this->assign("form_token", Helper::initFormToken($action));
            return $this->fetch();
        }
    }

    /**
     * 我的余额：分两种余额
     * 如果是商户角色，是商户对应的公众号里的余额
     * 如果是经销商角色，则是经销商表的余额
     */
    public function my(){
        $org_id = $this->org_id;
        $type = session('admin.type');
        $rows = [];
        $where = [];
        $where[] = ['t.org_id', '=', $org_id];
        // $where[] = ['t.type', '=', $type];
        if($type == 1){//商户账号
            $row = db('org t')->where($where)->find();
            $rows = [
                [
                    'name' => $row['name'],
                    'balance' => $row['balance'],
                ],
            ];
        }elseif($type == 3){//经销商账号
            $agency_id = session('admin.agency_id');
            if(empty($agency_id)){
                $this->error('账号还没有关联经销商，请联系平台管理员操作!!!');
            }
            $where[] = ['t.agency_id', 'in', $agency_id];
            $list = db('org_agency t')->where($where)->select();
            foreach ($list as $v) {
                $rows[] = [
                    'name' => base64_decode($v['agency_name']),
                    'balance' => $v['balance'],
                    'agency_id' => $v['agency_id'],
                ];
            }
        }
        $this->assign('rows', $rows);
        return $this->fetch();
    }

    /**
     * 余额变更记录表
     */
    public function bill()
    {
        $org_id = $this->org_id;
        $agency_id = input('agency_id', 0);
        $size = request()->param('size');
        $filter['keyword'] = input('keyword', '');
        // $filter['obj'] = input('obj', '');
        $where = [];
        $where[] = ['t.org_id', '=', $org_id];
        if ($agency_id > 0) {
            $where[] = ['t.agency_id', '=', $agency_id];
        }
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['a.name', 'like', "%$keyword%"];
        }
        // if (!empty($filter['obj']) and $filter['obj'] = 'my') {
        //     $where[] = ['t.obj', '=', 1];
        // }
        
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_balance_bill t')
            ->leftJoin("org o","t.org_id = o.org_id")
            ->leftJoin("org_agency a","t.agency_id = a.agency_id")
            ->where($where)
            ->field("t.*, o.name org_name, a.agency_name")
            ->order('t.create_time desc')
            ->paginate($pagesize);
        // echo DB::getLastSql();exit;
        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        
        return $this->fetch();
    }

    public function pay(){
        
    }

    /**
     * 消费记录
     */
    public function consume()
    {
        $org_id = $this->org_id;
        $agency_id = input('agency_id', 0);
        $size = request()->param('size');
        $filter['keyword'] = input('keyword', '');
        $filter['obj'] = input('obj', '');
        $where = [];
        $where[] = ['t.org_id', '=', $org_id];
        if ($agency_id > 0) {
            $where[] = ['t.agency_id', '=', $agency_id];
        }
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['a.name', 'like', "%$keyword%"];
        }
        if (!empty($filter['obj']) and $filter['obj'] = 'my') {
            $where[] = ['t.obj', '=', 2];
        }
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_balance_bill t')
            ->leftJoin("org o","t.org_id = o.org_id")
            ->leftJoin("org_agency a","t.agency_id = a.agency_id")
            ->where($where)
            ->field("t.*, o.name org_name, a.agency_name")
            ->order('t.id desc')
            ->paginate($pagesize);
        echo DB::getLastSql();exit;
        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        
        return $this->fetch();
    }

    private function _getDataList($where){
        $org_id = $this->org_id;
        $agency_id = input('agency_id', 0);
        $size = request()->param('size');
        $filter['keyword'] = input('keyword', '');
        $filter['obj'] = input('obj', '');
        $where = [];
        $where[] = ['t.org_id', '=', $org_id];
        if ($agency_id > 0) {
            $where[] = ['t.agency_id', '=', $agency_id];
        }
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $where[] = ['a.name', 'like', "%$keyword%"];
        }
        if (!empty($filter['obj']) and $filter['obj'] = 'my') {
            $where[] = ['t.obj', '=', 1];
        }
        
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_balance_bill t')
            ->leftJoin("org o","t.org_id = o.org_id")
            ->leftJoin("org_agency a","t.agency_id = a.agency_id")
            ->where($where)
            ->field("t.*, o.name org_name, a.agency_name")
            ->order('t.create_time desc')
            ->paginate($pagesize);
        // echo DB::getLastSql();exit;
        // 获取分页显示
        $page = $rows->render();

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
    }
}
