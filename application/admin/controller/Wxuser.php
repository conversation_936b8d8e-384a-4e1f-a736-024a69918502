<?php

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use app\admin\Service\Helper;

class Wxuser extends Base
{
    /**
     * * 会员
     */
    public function index()
    {
        $org_id = $this->org_id;
        $size = request()->param('size');
        $filter['wxgzh_id'] = input('wxgzh_id', '');
        $filter['type'] = input('type', '');
        $filter['agency_id'] = input('agency_id', '');
        $filter['tube_id'] = input('tube_id', '');
        $where = [];
        $where[] = ['t1.org_id', '=', $org_id];
        $where[] = ['t1.status', '=', 9];
        $where[] = ['t1.type', '=', 3];
        
        if (!empty($filter['wxgzh_id'])) {
            $where[] = ['t1.wxgzh_id', '=', $filter['wxgzh_id']];
        }
        if (!empty($filter['type'])) {
            $where[] = ['t1.type', '=', $filter['type']];
        }
        if (!empty($filter['agency_id'])) {
            $where[] = ['t1.agency_id', '=', $filter['agency_id']];
        }
        if (!empty($filter['tube_id'])) {
            $where[] = ['t1.tube_id', '=', $filter['tube_id']];
        }
        //***如果登录账号是：经销商
        $where = new_agency_where($where, 't1');
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_wxuser')->alias('t1')
            ->leftJoin("org_wxgzh t2","t1.wxgzh_id = t2.id")
            ->leftJoin("org_agency t3","t1.agency_id = t3.agency_id")
            ->leftJoin("org_agency_tube t4","t1.tube_id = t4.tube_id")
            ->where($where)
            ->field('t1.*,t2.name as gzh_name,t3.agency_name,t4.tube_name')
            ->order('t1.create_time desc')
            ->paginate($pagesize);
        
        // 获取分页显示
        $page = $rows->render();

        //获取经销商列表
        $agency_list = getAgencyList($org_id, 9);
        $this->assign('agency_list', $agency_list);

        //获取群管
        $tube_list = getAgencyTubeList($org_id, 9);
        $this->assign('tube_list', $tube_list);

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    /**
     * * 公海
     */
    public function open()
    {
        $org_id = $this->org_id;
        $size = request()->param('size');
        $filter['wxgzh_id'] = input('wxgzh_id', '');
        $filter['type'] = input('type', '');
        $filter['agency_id'] = input('agency_id', '');
        $filter['tube_id'] = input('tube_id', '');
        $where = [];
        $where[] = ['t1.org_id', '=', $org_id];
        $where[] = ['t1.status', '=', 9];
        $where[] = ['t1.type', '=', 4];
        if (!empty($filter['wxgzh_id'])) {
            $where[] = ['t1.wxgzh_id', '=', $filter['wxgzh_id']];
        }
        if (!empty($filter['type'])) {
            $where[] = ['t1.type', '=', $filter['type']];
        }
        if (!empty($filter['agency_id'])) {
            $where[] = ['t1.agency_id', '=', $filter['agency_id']];
        }
        if (!empty($filter['tube_id'])) {
            $where[] = ['t1.tube_id', '=', $filter['tube_id']];
        }

        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_wxuser')->alias('t1')
            ->leftJoin("org_wxgzh t2","t1.wxgzh_id = t2.id")
            ->leftJoin("org_agency t3","t1.agency_id = t3.agency_id")
            ->leftJoin("org_agency_tube t4","t1.tube_id = t4.tube_id")
            ->where($where)
            ->field('t1.*,t2.name as gzh_name,t3.agency_name,t4.tube_name')
            ->order('t1.create_time desc')
            ->paginate($pagesize);
        
        // 获取分页显示
        $page = $rows->render();

        //获取经销商列表
        $agency_list = getAgencyList($org_id, 9);
        $this->assign('agency_list', $agency_list);

        //获取群管
        $tube_list = getAgencyTubeList($org_id, 9);
        $this->assign('tube_list', $tube_list);

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    /**
     * 会员解除注册
    */
    public function delete()
    {
        $params = input();
        if (isset($params['user_id']) && !empty($params['user_id'])) {
            $map[] = ['user_id', '=', $params['user_id']];
            $map[] = ['org_id', '=', $this->org_id];
            $map[] = ['type', '=', 3];
            //更新wxuser表type为公海,清除wxuser表agency_id，tube_id
            $update_data = [
                'type' => 4, 
                'agency_id' => 0, 
                'tube_id' => 0, 
                'pro_tube_id' => db('org_wxuser')->where($map)->value('tube_id'),
                'update_time' => time(),
            ];
            $res = db('org_wxuser')->where($map)->update($update_data);
            if($res){
                $this->success("保存成功", url('index'));
            }else{
                $this->error('解除注册失败');
            }
        } else {
            $this->error('信息有误，请重新操作！', url('index'));
        }
    }

}
