<?php

namespace app\admin\controller;

use think\Controller;
use \Auth\Auth;

use app\admin\Service\AliyunVodService;
use AlibabaCloud\SDK\Vod\V20170321\Models\CreateUploadVideoRequest;
use AlibabaCloud\SDK\Vod\V20170321\Models\GetVideoPlayAuthRequest;
use AlibabaCloud\SDK\Vod\V20170321\Models\GetPlayInfoRequest;

use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Console\Console;
use AlibabaCloud\Tea\Utils\Utils;

class Aliyun extends Controller
{
    protected $org_id;

    protected function initialize()
    {
        parent::initialize();
        $Auth = new Auth();
        if (!$Auth->checkLogin()) {
            $this->redirect(url(Config('user_auth_gateway')));
        }
    }

    //获取音视频上传地址和凭证
    public function get_vod_createUpload(){
        $params = input();
        $client = AliyunVodService::createClient();
        $postData = [
            'title' => trim(input('title', 'Default Title')),
            'fileName' => trim(input('fileName', 'default.mp4')),
            'fileDuration' => trim(input('fileDuration', 0)),
            'fileExt' => trim(input('fileExt', 'mp4')),
        ];
        $createUploadVideoRequest = new CreateUploadVideoRequest($postData);
        $runtime = new RuntimeOptions([]);
        try {
            $resp = $client->createUploadVideoWithOptions($createUploadVideoRequest, $runtime);
            return json(array('status' => 200, 'info' => 'success', 'data' => $resp));
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            var_dump($error->message);
            // 诊断地址
            var_dump($error->data["Recommend"]);
            Utils::assertAsString($error->message);
        }
        // return json(array('status' => 0, 'info' => '没有数据', 'data' => ''));
    }


    //获取音视频播放凭证
    public function get_vod_playAuth($vid){
        $params = input();
        $client = AliyunVodService::createClient();

        $postData = [
            'videoId' => $vid,
            'tuthInfoTimeout' => 2000,
            'apiVersion' => trim(input('fileDuration', 0)),
        ];
        $getVideoPlayAuthRequest = new GetVideoPlayAuthRequest($postData);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $resp = $client->getVideoPlayAuthWithOptions($getVideoPlayAuthRequest, $runtime);
            return json(array('status' => 200, 'info' => 'success', 'data' => $resp));
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            var_dump($error->message);
            // 诊断地址
            var_dump($error->data["Recommend"]);
            Utils::assertAsString($error->message);
        }
    }

    //获取音视频播放地址
    public function get_vod_playInfo($vid){
        $params = input();
        $client = AliyunVodService::createClient();

        $postData = [
            'videoId' => $vid,
            // 'tuthInfoTimeout' => 2000,
            // 'apiVersion' => trim(input('fileDuration', 0)),
        ];
        $getPlayInfoRequest = new GetPlayInfoRequest($postData);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $resp = $client->getPlayInfoWithOptions($getPlayInfoRequest, $runtime);
            return json(array('status' => 200, 'info' => 'success', 'data' => $resp));
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            var_dump($error->message);
            // 诊断地址
            var_dump($error->data["Recommend"]);
            Utils::assertAsString($error->message);
        }
    }
}
