<?php

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use app\admin\Service\Helper;

class AgencyTube extends Base
{
    /**
     * * 群管
     */
    public function index()
    {
        $org_id = $this->org_id;
        $size = request()->param('size');
        $filter['status'] = input('status', 9);
        $filter['wxgzh_id'] = input('wxgzh_id', '');
        $filter['type'] = input('type', '');
        $filter['agency_id'] = input('agency_id', '');
        $where = [];
        $where[] = ['t1.org_id', '=', $org_id];
        $where[] = ['t1.status', '=', $filter['status']];
        if (!empty($filter['wxgzh_id'])) {
            $where[] = ['t1.wxgzh_id', '=', $filter['wxgzh_id']];
        }
        if (!empty($filter['type'])) {
            $where[] = ['t1.type', '=', $filter['type']];
        }
        if (!empty($filter['agency_id'])) {
            $where[] = ['t1.agency_id', '=', $filter['agency_id']];
        }
        //***如果登录账号是：经销商
        $where = new_agency_where($where, 't1');
        //每页显示几条数据
        $pagesize = $size ? $size : config('paginate.pagesize');
        $rows = db('org_agency_tube')->alias('t1')
            ->leftJoin("org_wxgzh t2","t1.wxgzh_id = t2.id")
            ->leftJoin("org_wxuser t3","t3.user_id = t1.wx_user_id")
            ->where($where)
            ->field('t1.*,t2.name as gzh_name,t3.last_visit')
            ->order('t1.create_time desc')
            ->paginate($pagesize);
        
        // 获取分页显示
        $page = $rows->render();

        //获取经销商列表
        $agency_list = getAgencyList($org_id, 9);
        $this->assign('agency_list', $agency_list);

        $this->assign('rows', $rows);
        $this->assign('page', $page);
        $this->assign('filter', $filter);
        return $this->fetch();
    }

    /**
     * 群管解除注册
    */
    public function delete()
    {
        $params = input();
        if (isset($params['tube_id']) && !empty($params['tube_id'])) {
            $map[] = ['tube_id', '=', $params['tube_id']];
            $map[] = ['org_id', '=', $this->org_id];
            //更新群管表del，更新wxuser表type为公海,清除wxuser表agency_id，tube_id
            $res = db('org_agency_tube')->where($map)->update(['del' => 1]);
            if($res){
                $wx_user_id = db('org_agency_tube')->where('tube_id', $params['tube_id'])->value('wx_user_id');
                db('org_wxuser')->where('user_id', $wx_user_id)->update(['type' => 4, 'agency_id' => 0, 'tube_id' => 0, 'update_time' => time()]);
                $this->success("保存成功", url('index'));
            }else{
                $this->error('解除注册失败');
            }
        } else {
            $this->error('信息有误，请重新操作！', url('index'));
        }
    }

}
