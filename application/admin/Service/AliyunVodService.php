<?php

/**
 * @file            AliyunVodService.php
 * @version         1.0
 * @description     阿里云视频点播服务类
 */

namespace app\admin\Service;

use AlibabaCloud\SDK\Vod\V20170321\Vod;
use AlibabaCloud\Credentials\Credential;
use AlibabaCloud\Credentials\Credential\Config as CredentialConfig; 
use Darabonba\OpenApi\Models\Config;

use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\SDK\Vod\V20170321\Models\GetVideoInfoRequest;
use AlibabaCloud\SDK\Vod\V20170321\Models\GetVideoInfosRequest;

class AliyunVodService
{
    /**
     * 使用凭据初始化账号Client
     * @return Vod Client
     */
    public static function createClient(){
        $env = config('resource.aliyun_vod');
        // 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/311677.html。
        $credConfig = new CredentialConfig([
            'type' => 'access_key',
            'accessKeyId' => $env['access_key_id'],
            'accessKeySecret' => $env['access_key_secret'],
        ]);
        $credClient = new Credential($credConfig);
        $vodConfig = new Config([
            "credential" => $credClient,
        ]);
        // Endpoint 请参考 https://api.aliyun.com/product/vod
        $vodConfig->endpoint = "vod.cn-beijing.aliyuncs.com";
        return new Vod($vodConfig);
    }



    //获取单个音视频信息
    public static function get_vod_videoInfo($vid){
        $params = input();
        $client = AliyunVodService::createClient();

        $postData = [
            'videoId' => $vid,
        ];
        $getVideoInfoRequest = new GetVideoInfoRequest($postData);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            return $client->getVideoInfoWithOptions($getVideoInfoRequest, $runtime);
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            var_dump($error->message);
            // 诊断地址
            var_dump($error->data["Recommend"]);
            Utils::assertAsString($error->message);
        }
    }

    //批量获取音视频信息:最多20个
    public static function get_vod_videoInfos($list_vid){
        $params = input();
        $client = AliyunVodService::createClient();

        $postData = [
            'videoIds' => $list_vid,
        ];
        $getVideoInfosRequest = new GetVideoInfosRequest($postData);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            return $client->getVideoInfosWithOptions($getVideoInfosRequest, $runtime);
        }
        catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            var_dump($error->message);
            // 诊断地址
            var_dump($error->data["Recommend"]);
            Utils::assertAsString($error->message);
        }
    }
}
