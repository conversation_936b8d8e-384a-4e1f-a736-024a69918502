<?php
namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\Exception;
use think\Db;
use EasyWeChat\Factory;
use think\facade\Log;

/**
 * 发送红包
 */
class DoPay extends Command
{

    protected function configure()
    {
        $this->setName('DoPay')->setDescription('Send A Red Package to User');
    }

    protected function execute()
    {
        $where = ["order_status"=>11];
        $list = db('order')->where($where)->limit(1)->select();
        // echo DB::getLastSql();exit;
        if(count($list) == 0){
            return;
        }
        foreach ($list as $k => $order) {
            //****首先判断agency余额，余额不足1元，不继续执行****
            $balance = db('org_agency')->where('agency_id', $order['agency_id'])->value('balance');
            if($balance < 1){
                $this->_processOrderError($order, 12);//12: 表示经销商余额不足
                echo "Error:agency_id=".$order['agency_id']."\r\n";
                continue;
            }
            //查找用户和公众号
            $user = db('org_wxuser t1')
                ->leftJoin("org_wxgzh t2","t1.wxgzh_id = t2.id")
                ->field("t1.org_id, t1.agency_id, t1.openid, t2.account")
                ->where("user_id", $order['user_id'])->find();
            // echo DB::getLastSql();exit;
            //第1步：
            $config = config('resource.wxsh_duolian');//存的是duolian公众号
            $config['app_id'] = $user['account'];//替换为用户对应的wx_app_id
            $payment = Factory::payment($config);

            $redpack = $payment->redpack;
            $redpackData = [
                'mch_billno'   => $order['order_id'],
                'send_name'    => '答题奖励',
                're_openid'    => $user['openid'],
                'total_num'    => 1,  //固定为1，可不传
                'total_amount' => $order['order_amount'] * 100,  //单位为分，不小于100
                'wishing'      => '恭喜发财',
                'client_ip'    => '',  //可不传，不传则由 SDK 取当前客户端 IP
                'act_name'     => '测试活动',
                'remark'       => '测试备注',
                // 'scene_id'     => 'PRODUCT_1',//如果传入参数会报错
            ];
            $result = $redpack->sendNormal($redpackData);
            Log::notice($result); 
            //不管成功与否，先把微信返回的结果记录下来
            $logAttr = array(
                'order_id' => $order['order_id'],
                'out_trade_no' => $result['mch_billno'],
                'pay_id' => 1,//微信
                'transaction_id' => $result['send_listid'],
                'amount' => $result['total_amount'] / 100,
                'code' => $result['result_code'],
                'create_time' => time(),
                'response' => json_encode($result),
            );
            /////////////// Log::notice($logAttr); 通过记录log才查出的问题，太坑/////////////
            $id = db("notify_paylog")->insertGetId($logAttr);

            if ($result['return_code'] === 'SUCCESS') {
                // 红包发送成功
                if ($result['result_code'] === 'SUCCESS') {
                    $this->_processOrderSuccess($order, 15);
                    echo $order['order_id'].":ok\r\n";
                }elseif($result['result_code'] === 'FAIL') {
                    //失败的处理逻辑
                    $this->_processOrderError($order, 14);//14: wx接口返回失败
                    echo "WX-Error:".json_encode($result)."\r\n";
                }
            }else {
                echo "WX:通信失败，请稍后再通知我!\r\n";
            }
        }
    }

    /**
     * 处理订单失败状态
     */
    private function _processOrderError($order, $status){
        $data = [
            'order_id' => $order['order_id'],
            'order_status' => $status,
            'pay_time' => time(),
        ];
        if (Db::name('order')->update($data) === false) {
            throw new Exception("订单表更新失败-7001", 1);
        }
    }

    /**
     * 处理订单成功状态
     */
    private function _processOrderSuccess($order, $status){
        $time = time();
        Db::startTrans();
        try {
            $amount = $order['order_amount'];
            $billData = [
                'order_id' => $order['order_id'],
                'org_id' => $order['org_id'],
                'agency_id' => $order['agency_id'],
                'type' => 2,
                'obj' => $order['obj'],
                'operator_id' => $order['operator_id'],
                'operator_name' => $order['operator_name'],
                'create_time' => $time,
                'remark' => '红包发送成功，余额减少：'.$amount,
            ];
            $agency = Db::name('org_agency')->where('agency_id',$order['agency_id'])->find();
            $billData['balance_before'] = $agency['balance'];
            $billData['amount'] = $amount;
            //增加agency的余额
            $sql = "update ksd_org_agency set balance = balance - $amount where agency_id = ".$order['agency_id'];
            $n = Db::execute($sql);

            $billData['balance_after'] = $billData['balance_before'] - $amount;
            $id = Db::name('org_balance_bill')->insertGetId($billData);

            $uptOrder = [
                'order_id' => $order['order_id'],
                'order_status' => $status,
                'pay_time' => $time,
                'update_time' => $time,
            ];
            if (Db::name('order')->update($uptOrder) === false) {
                throw new Exception("订单表更新失败-7001", 1);
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $result = array("code"=>400,"msg"=>$e->getMessage());
            return $result;
            exit;
        }
    }
}