<?php
namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\Exception;
use think\Db;
use EasyWeChat\Factory;
use think\facade\Log;

/**
 * 处理完播
 */
class DoFinish extends Command
{

    protected function configure()
    {
        $this->setName('DoFinish')->setDescription('Process the user who have completed the video');
    }

    protected function execute()
    {
        //1.完成播放，2.答题正确，3.还未处理
        $where = ["is_finish"=>1, "is_correct"=>1, "is_exec"=>0];
        $list = db('org_user_watch')->where($where)->limit(1)->select();
        if(count($list) == 0){
            return;
        }
        foreach ($list as $k => $wb) {
            $user = db('org_wxuser t1')
                ->leftJoin("org_wxgzh t2","t1.wxgzh_id = t2.id")
                ->field("t1.org_id, t1.agency_id, t1.openid, t2.account")
                ->where("user_id", $wb['user_id'])->find();
            // echo DB::getLastSql();
            //****首先判断agency是否配置有红包规则：类型为1的答题奖励，并且金额答应0****
            $cond1 = ['agency_id'=>$user['agency_id'], 'cate_id'=>1];
            $rule = db('org_red_rule')->where($cond1)->find();
            //没有规则，或者红包金额为0，不处理
            if(empty($rule) or $rule['red_amount'] == 0){
                $this->_processWatch($wb['id'], 2, '经销商无红包规则or红包金额为0');
                echo "Error:agency_id=".$user['agency_id']."\r\n";
                continue;
            }
            //创建红包订单
            $order = $this->_createOrder($wb, $rule);
            $this->_processWatch($wb['id'], 1, '成功创建红包订单');
            echo $order['order_id'].":ok\r\n";
            sleep(1);
        }
    }

    /**
     * 处理完播表的数据
     */
    private function _processWatch($id, $code, $desc){
        $data = [
            'id' => $id,
            'is_exec' => 1,
            'exec_code' => $code,
            'exec_desc' => $desc,
        ];
        if (Db::name('org_user_watch')->update($data) === false) {
            throw new Exception("完播表更新失败-6001", 1);
        }
    }

    /**
     * 创建红包订单
     */
    private function _createOrder($row, $rule){
        $time = time();
        $data = [];
        $data['order_id'] = getUUIDPro('HB');
        $data['org_id'] = $row['org_id'];
        $data['agency_id'] = $row['agency_id'];
        $data['tube_id'] = $row['tube_id'];
        $data['user_id'] = $row['user_id'];
        $data['order_status'] = 11;
        $data['order_type'] = 3;
        $data['obj'] = 5;//发红包
        $data['redrule_id'] = $rule['id'];
        $data['amount'] = $rule['red_amount'];
        $data['service_fee'] = 0;
        $data['order_amount'] = $data['amount'] + $data['service_fee'];
        $data['operator_id'] = 0;
        $data['operator_name'] = "System";
        $data['create_time'] = $time;
        $data['update_time'] = $time;
        //第一步：先创建订单
        if (!Db::name("order")->insert($data)) {
            throw new Exception("创建订单失败", 1);
        }
        return $data;
    }
}