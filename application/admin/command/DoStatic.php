<?php
namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\Exception;
use think\Db;
use think\facade\Log;

/**
 * 群管统计数据处理
 */
class DoStatic extends Command
{
    /**
     * 配置命令
     */
    protected function configure()
    {
        $this->setName('DoStatic')
            ->addOption('tube_id', 't', Option::VALUE_OPTIONAL, '指定群管ID进行统计')
            ->addOption('date', 'd', Option::VALUE_OPTIONAL, '指定统计日期(Y-m-d格式)')
            ->addOption('force', 'f', Option::VALUE_NONE, '强制重新统计已存在的数据')
            ->addOption('clean', 'c', Option::VALUE_NONE, '清理过期的统计数据')
            ->addOption('range', 'r', Option::VALUE_OPTIONAL, '统计日期范围，格式：开始日期,结束日期')
            ->setDescription('根据watch表数据统计群管的播放、完播、答题、答对人数');
    }

    /**
     * 执行命令
     */
    protected function execute(Input $input, Output $output)
    {
        $tubeId = $input->getOption('tube_id');
        $date = $input->getOption('date') ?: date('Y-m-d');
        $force = $input->getOption('force');
        $clean = $input->getOption('clean');
        $range = $input->getOption('range');

        $output->writeln("开始执行群管统计任务...");

        try {
            // 获取配置
            $config = config('static.static_config');

            // 如果是清理模式
            if ($clean) {
                $this->cleanExpiredData($output, $config);
                return;
            }

            // 处理日期范围
            $dates = $this->parseDateRange($date, $range);

            foreach ($dates as $currentDate) {
                $output->writeln("统计日期: {$currentDate}");

                // 验证日期格式
                if (!$this->validateDate($currentDate)) {
                    throw new Exception("日期格式错误，请使用 Y-m-d 格式");
                }

                // 获取需要统计的群管列表
                $tubeList = $this->getTubeList($tubeId);

                if (empty($tubeList)) {
                    $output->writeln("没有找到需要统计的群管数据");
                    continue;
                }

                $output->writeln("找到 " . count($tubeList) . " 个群管需要统计");

                $successCount = 0;
                $errorCount = 0;

                foreach ($tubeList as $tube) {
                    try {
                        $result = $this->processStatistic($tube, $currentDate, $force);

                        if ($result) {
                            $successCount++;
                            $output->writeln("群管 {$tube['tube_name']}(ID:{$tube['tube_id']}) 统计完成");
                        } else {
                            $errorCount++;
                            $output->writeln("群管 {$tube['tube_name']}(ID:{$tube['tube_id']}) 统计跳过（数据已存在）");
                        }

                    } catch (Exception $e) {
                        $errorCount++;
                        $output->writeln("群管 {$tube['tube_name']}(ID:{$tube['tube_id']}) 统计失败: " . $e->getMessage());

                        // 记录错误日志
                        if ($config['enable_log']) {
                            Log::error("DoStatic统计失败", [
                                'tube_id' => $tube['tube_id'],
                                'date' => $currentDate,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }

                $output->writeln("日期 {$currentDate} 统计完成！成功: {$successCount}, 失败/跳过: {$errorCount}");
            }

            $output->writeln("所有统计任务完成！");

        } catch (Exception $e) {
            $output->writeln("统计任务执行失败: " . $e->getMessage());
            Log::error("DoStatic命令执行失败", ['error' => $e->getMessage()]);
        }
    }

    /**
     * 验证日期格式
     */
    private function validateDate($date)
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * 获取群管列表
     */
    private function getTubeList($tubeId = null)
    {
        $where = ['status' => 1]; // 只统计启用的群管
        
        if ($tubeId) {
            $where['tube_id'] = $tubeId;
        }
        
        return Db::name('org_agency_tube')
            ->where($where)
            ->field('tube_id, tube_name, agency_id')
            ->select();
    }

    /**
     * 处理单个群管的统计
     */
    private function processStatistic($tube, $date, $force = false)
    {
        $tubeId = $tube['tube_id'];
        $tableName = config('static.static_config.table_name');
        
        // 检查是否已存在统计数据
        $exists = Db::name(str_replace('org_', '', $tableName))
            ->where([
                'tube_id' => $tubeId,
                'stat_date' => $date
            ])
            ->find();
            
        if ($exists && !$force) {
            return false; // 数据已存在且不强制更新
        }
        
        // 计算统计数据
        $statistics = $this->calculateStatistics($tubeId, $date);
        
        // 准备保存的数据
        $data = [
            'tube_id' => $tubeId,
            'tube_name' => $tube['tube_name'],
            'agency_id' => $tube['agency_id'],
            'stat_date' => $date,
            'play_count' => $statistics['play_count'],
            'finish_count' => $statistics['finish_count'],
            'answer_count' => $statistics['answer_count'],
            'correct_count' => $statistics['correct_count'],
            'create_time' => time(),
            'update_time' => time(),
        ];
        
        // 保存或更新数据
        if ($exists) {
            $data['id'] = $exists['id'];
            $data['update_time'] = time();
            unset($data['create_time']);
            
            $result = Db::name(str_replace('org_', '', $tableName))->update($data);
        } else {
            $result = Db::name(str_replace('org_', '', $tableName))->insert($data);
        }
        
        return $result !== false;
    }

    /**
     * 计算统计数据
     */
    private function calculateStatistics($tubeId, $date)
    {
        // 构建基础查询条件
        $baseWhere = [
            'tube_id' => $tubeId,
            ['create_time', 'between', [
                strtotime($date . ' 00:00:00'),
                strtotime($date . ' 23:59:59')
            ]]
        ];
        
        // 播放人数（总观看记录数）
        $playCount = Db::name('org_user_watch')
            ->where($baseWhere)
            ->count('DISTINCT user_id');
            
        // 完播人数
        $finishCount = Db::name('org_user_watch')
            ->where($baseWhere)
            ->where('is_finish', 1)
            ->count('DISTINCT user_id');
            
        // 答题人数
        $answerCount = Db::name('org_user_watch')
            ->where($baseWhere)
            ->where('answer_id', '>', 0)
            ->count('DISTINCT user_id');
            
        // 答对人数
        $correctCount = Db::name('org_user_watch')
            ->where($baseWhere)
            ->where('is_correct', 1)
            ->count('DISTINCT user_id');
        
        return [
            'play_count' => $playCount,
            'finish_count' => $finishCount,
            'answer_count' => $answerCount,
            'correct_count' => $correctCount,
        ];
    }

    /**
     * 解析日期范围
     */
    private function parseDateRange($date, $range)
    {
        if ($range) {
            $rangeParts = explode(',', $range);
            if (count($rangeParts) === 2) {
                $startDate = trim($rangeParts[0]);
                $endDate = trim($rangeParts[1]);

                if ($this->validateDate($startDate) && $this->validateDate($endDate)) {
                    $dates = [];
                    $current = strtotime($startDate);
                    $end = strtotime($endDate);

                    while ($current <= $end) {
                        $dates[] = date('Y-m-d', $current);
                        $current = strtotime('+1 day', $current);
                    }

                    return $dates;
                }
            }
        }

        return [$date];
    }

    /**
     * 清理过期数据
     */
    private function cleanExpiredData($output, $config)
    {
        $output->writeln("开始清理过期统计数据...");

        $retentionDays = $config['retention_days'] ?? 30;
        $expireDate = date('Y-m-d', strtotime("-{$retentionDays} days"));
        $tableName = config('static.static_config.table_name');

        try {
            $count = Db::name(str_replace('org_', '', $tableName))
                ->where('stat_date', '<', $expireDate)
                ->delete();

            $output->writeln("清理完成，删除了 {$count} 条过期数据（{$expireDate} 之前的数据）");

            // 记录日志
            if ($config['enable_log']) {
                Log::info("DoStatic清理过期数据", [
                    'expire_date' => $expireDate,
                    'deleted_count' => $count
                ]);
            }

        } catch (Exception $e) {
            $output->writeln("清理过期数据失败: " . $e->getMessage());
            Log::error("DoStatic清理过期数据失败", ['error' => $e->getMessage()]);
        }
    }
}
