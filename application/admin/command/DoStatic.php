<?php
namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\Exception;
use think\Db;
use think\facade\Log;

/**
 * 群管统计数据处理
 */
class DoStatic extends Command
{
    /**
     * 配置命令
     */
    protected function configure()
    {
        $this->setName('DoStatic')
            ->addOption('tube_id', 't', Option::VALUE_OPTIONAL, '指定群管ID进行统计')
            ->addOption('date', 'd', Option::VALUE_OPTIONAL, '指定统计日期(Y-m-d格式)')
            ->addOption('force', 'f', Option::VALUE_NONE, '强制重新统计已存在的数据')
            ->addOption('clean', 'c', Option::VALUE_NONE, '清理过期的统计数据')
            ->addOption('range', 'r', Option::VALUE_OPTIONAL, '统计日期范围，格式：开始日期,结束日期')
            ->addOption('hourly', 'h', Option::VALUE_NONE, '按小时统计（默认按天统计）')
            ->addOption('agency_id', 'a', Option::VALUE_OPTIONAL, '指定经销商ID进行统计')
            ->setDescription('根据watch表数据统计群管的观看、完播、答题、答对人数');
    }

    /**
     * 执行命令
     */
    protected function execute(Input $input, Output $output)
    {
        $tubeId = $input->getOption('tube_id');
        $date = $input->getOption('date') ?: date('Y-m-d');
        $force = $input->getOption('force');
        $clean = $input->getOption('clean');
        $range = $input->getOption('range');
        $hourly = $input->getOption('hourly');
        $agencyId = $input->getOption('agency_id');

        $output->writeln("开始执行群管统计任务...");

        try {
            // 获取配置
            $config = config('static.static_config');

            // 如果是清理模式
            if ($clean) {
                $this->cleanExpiredData($output, $config);
                return;
            }

            // 处理日期范围
            $dates = $this->parseDateRange($date, $range);

            foreach ($dates as $currentDate) {
                $output->writeln("统计日期: {$currentDate}");

                // 验证日期格式
                if (!$this->validateDate($currentDate)) {
                    throw new Exception("日期格式错误，请使用 Y-m-d 格式");
                }

                // 获取需要统计的群管列表
                $tubeList = $this->getTubeList($tubeId, $agencyId);

                if (empty($tubeList)) {
                    $output->writeln("没有找到需要统计的群管数据");
                    continue;
                }

                $output->writeln("找到 " . count($tubeList) . " 个群管需要统计");

                $successCount = 0;
                $errorCount = 0;

                foreach ($tubeList as $tube) {
                    try {
                        $result = $this->processStatistic($tube, $currentDate, $force, $hourly);

                        if ($result) {
                            $successCount++;
                            $output->writeln("群管 {$tube['tube_name']}(ID:{$tube['tube_id']}) 统计完成");
                        } else {
                            $errorCount++;
                            $output->writeln("群管 {$tube['tube_name']}(ID:{$tube['tube_id']}) 统计跳过（数据已存在）");
                        }

                    } catch (Exception $e) {
                        $errorCount++;
                        $output->writeln("群管 {$tube['tube_name']}(ID:{$tube['tube_id']}) 统计失败: " . $e->getMessage());

                        // 记录错误日志
                        if ($config['enable_log']) {
                            Log::error("DoStatic统计失败", [
                                'tube_id' => $tube['tube_id'],
                                'date' => $currentDate,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }

                $output->writeln("日期 {$currentDate} 统计完成！成功: {$successCount}, 失败/跳过: {$errorCount}");
            }

            $output->writeln("所有统计任务完成！");

        } catch (Exception $e) {
            $output->writeln("统计任务执行失败: " . $e->getMessage());
            Log::error("DoStatic命令执行失败", ['error' => $e->getMessage()]);
        }
    }

    /**
     * 验证日期格式
     */
    private function validateDate($date)
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * 获取群管列表
     */
    private function getTubeList($tubeId = null, $agencyId = null)
    {
        $where = ['t1.status' => 1]; // 只统计启用的群管

        if ($tubeId) {
            $where['t1.tube_id'] = $tubeId;
        }

        if ($agencyId) {
            $where['t1.agency_id'] = $agencyId;
        }

        return Db::name('org_agency_tube')->alias('t1')
            ->leftJoin('org_agency t2', 't1.agency_id = t2.agency_id')
            ->leftJoin('org_wxgzh t3', 't2.wxgzh_id = t3.id')
            ->where($where)
            ->field('t1.tube_id, t1.tube_name, t1.agency_id, t2.agency_name, t2.org_id, t3.id as gzh_id, t3.name as gzh_name')
            ->select();
    }

    /**
     * 处理单个群管的统计
     */
    private function processStatistic($tube, $date, $force = false, $hourly = false)
    {
        $tubeId = $tube['tube_id'];
        $tableName = config('static.static_config.table_name');
        $config = config('static.static_config');

        // 解析日期
        $dateTime = strtotime($date);
        $year = date('Y', $dateTime);
        $month = date('n', $dateTime);
        $day = date('j', $dateTime);
        $hour = $hourly ? date('G', $dateTime) : ($config['default_hour'] ?? 0);

        // 检查是否已存在统计数据
        $exists = Db::name($tableName)
            ->where([
                'year' => $year,
                'month' => $month,
                'day' => $day,
                'hour' => $hour,
                'tube_id' => $tubeId
            ])
            ->find();

        if ($exists && !$force) {
            return false; // 数据已存在且不强制更新
        }

        // 计算统计数据
        $statistics = $this->calculateStatistics($tubeId, $date, $hourly ? $hour : null);

        // 准备保存的数据
        $data = [
            'year' => $year,
            'month' => $month,
            'day' => $day,
            'hour' => $hour,
            'org_id' => $tube['org_id'] ?? 0,
            'org_name' => '', // 可以后续补充
            'gzh_id' => $tube['gzh_id'] ?? 0,
            'gzh_name' => $tube['gzh_name'] ?? '',
            'agency_id' => $tube['agency_id'],
            'agency_name' => $tube['agency_name'] ?? '',
            'tube_id' => $tubeId,
            'tube_name' => $tube['tube_name'],
            'watch_count' => $statistics['watch_count'],
            'finish_count' => $statistics['finish_count'],
            'answer_count' => $statistics['answer_count'],
            'correct_count' => $statistics['correct_count'],
            'create_time' => time(),
            'update_time' => time(),
        ];

        // 保存或更新数据
        if ($exists) {
            $data['id'] = $exists['id'];
            $data['update_time'] = time();
            unset($data['create_time']);

            $result = Db::name($tableName)->update($data);
        } else {
            $result = Db::name($tableName)->insert($data);
        }

        return $result !== false;
    }

    /**
     * 计算统计数据
     */
    private function calculateStatistics($tubeId, $date, $hour = null)
    {
        // 构建基础查询条件
        if ($hour !== null) {
            // 按小时统计
            $baseWhere = [
                'tube_id' => $tubeId,
                ['create_time', 'between', [
                    strtotime($date . ' ' . sprintf('%02d:00:00', $hour)),
                    strtotime($date . ' ' . sprintf('%02d:59:59', $hour))
                ]]
            ];
        } else {
            // 按天统计
            $baseWhere = [
                'tube_id' => $tubeId,
                ['create_time', 'between', [
                    strtotime($date . ' 00:00:00'),
                    strtotime($date . ' 23:59:59')
                ]]
            ];
        }

        // 观看人数（总观看记录数）
        $watchCount = Db::name('org_user_watch')
            ->where($baseWhere)
            ->count('DISTINCT user_id');

        // 完播人数
        $finishCount = Db::name('org_user_watch')
            ->where($baseWhere)
            ->where('is_finish', 1)
            ->count('DISTINCT user_id');

        // 答题人数
        $answerCount = Db::name('org_user_watch')
            ->where($baseWhere)
            ->where('answer_id', '>', 0)
            ->count('DISTINCT user_id');

        // 答对人数
        $correctCount = Db::name('org_user_watch')
            ->where($baseWhere)
            ->where('is_correct', 1)
            ->count('DISTINCT user_id');

        return [
            'watch_count' => $watchCount,
            'finish_count' => $finishCount,
            'answer_count' => $answerCount,
            'correct_count' => $correctCount,
        ];
    }

    /**
     * 解析日期范围
     */
    private function parseDateRange($date, $range)
    {
        if ($range) {
            $rangeParts = explode(',', $range);
            if (count($rangeParts) === 2) {
                $startDate = trim($rangeParts[0]);
                $endDate = trim($rangeParts[1]);

                if ($this->validateDate($startDate) && $this->validateDate($endDate)) {
                    $dates = [];
                    $current = strtotime($startDate);
                    $end = strtotime($endDate);

                    while ($current <= $end) {
                        $dates[] = date('Y-m-d', $current);
                        $current = strtotime('+1 day', $current);
                    }

                    return $dates;
                }
            }
        }

        return [$date];
    }

    /**
     * 清理过期数据
     */
    private function cleanExpiredData($output, $config)
    {
        $output->writeln("开始清理过期统计数据...");

        $retentionDays = $config['retention_days'] ?? 90;
        $expireDateTime = strtotime("-{$retentionDays} days");
        $expireYear = date('Y', $expireDateTime);
        $expireMonth = date('n', $expireDateTime);
        $expireDay = date('j', $expireDateTime);
        $tableName = config('static.static_config.table_name');

        try {
            // 构建过期条件：年份小于过期年份，或者年份相等但月日小于过期月日
            $count = Db::name($tableName)
                ->where(function($query) use ($expireYear, $expireMonth, $expireDay) {
                    $query->where('year', '<', $expireYear)
                          ->whereOr(function($subQuery) use ($expireYear, $expireMonth) {
                              $subQuery->where('year', $expireYear)
                                       ->where('month', '<', $expireMonth);
                          })
                          ->whereOr(function($subQuery) use ($expireYear, $expireMonth, $expireDay) {
                              $subQuery->where('year', $expireYear)
                                       ->where('month', $expireMonth)
                                       ->where('day', '<', $expireDay);
                          });
                })
                ->delete();

            $expireDate = date('Y-m-d', $expireDateTime);
            $output->writeln("清理完成，删除了 {$count} 条过期数据（{$expireDate} 之前的数据）");

            // 记录日志
            if ($config['enable_log']) {
                Log::info("DoStatic清理过期数据", [
                    'expire_date' => $expireDate,
                    'deleted_count' => $count
                ]);
            }

        } catch (Exception $e) {
            $output->writeln("清理过期数据失败: " . $e->getMessage());
            Log::error("DoStatic清理过期数据失败", ['error' => $e->getMessage()]);
        }
    }
}
