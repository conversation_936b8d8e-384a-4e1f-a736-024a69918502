<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
    <!-- <style type="text/css">
        h1{text-align: center;}
        form{width:600px;  margin: auto; padding: 20px 50px; border: 1px solid #0091f2; border-radius: 10px; display: block; }
        input{ font-size: 24px; margin: 10px;}
        .clear{clear: both;}
        .speed_box{ width: 600px; height: 20px; display: none; border: 1px solid #0091f2; border-radius: 10px; overflow: hidden;}
        #file_box{ min-width:600px; min-height: 300px; border: 1px solid #0091f2; border-radius: 10px; display: inline-block; background: #EEE; overflow: hidden; z-index: 999999;}
        #speed{ width: 0; height: 100%; background: #0091f2; color: white; text-align: center; line-height: 20px; font-size: 16px;}
        #file_size ,#file_type{ display: inline-block;  padding: 0px 16px; font-size: 16px; color: #0091f2; font-weight: bold;}
        #file_type{ margin-top: 30px;}
        .opts_btn{ position:relative; display: inline-block; padding: 8px 16px; font-size:16px;color:white; text-decoration: none;background:#0091f2; border: 2px solid #0091f2; border-radius: 3px; cursor: pointer; overflow: hidden;}
        .oFile{position:absolute;width:100%;height:100%;z-index: 10;top:0px;left:0px;opacity: 0;}
        .send_btn{ display: inline-block; display: none; float: right; margin-top: 20px; padding: 8px 16px; font-size: 16px; color:white; background: #0091f2; border: 1px solid transparent; border-radius: 2px; cursor: pointer;}
    </style> -->
    <style type="text/css">
    #file_box{ min-width:600px; min-height: 300px; border: 1px solid #0091f2; border-radius: 10px; display: inline-block; background: #EEE; overflow: hidden; z-index: 999999; text-align: center;}
    </style>
</head>

<body class="gray-bg">

<div class="wrapper">
    <div class="ibox">
        <div class="ibox-title">

            <h5>视频信息</h5>
            <div class="ibox-tools">
                <a id="page-goback" href="/Video/index">
                    <i class="fa fa-arrow-left"></i> 后退
                </a>
                <a id="page-refresh" href="javascript:;">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
        </div>
        <div class="ibox-content">

            <form class="form-horizontal validate" method="post" action="" autocomplete="off" enctype="multipart/form-data">

                <div class="form-group">
                    <label class="col-sm-2 control-label">视频名称</label>
                    <div class="col-sm-3">
                        <input type="text" name="video_name" id="video_name" class="form-control" value="{{$row['video_name']}}" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">分类</label>
                    <div class="col-sm-3">
                        <select name="cate_id" class="form-control" required>
                            <option value="">请选择</option>
                            {{volist name="cate_list" id="v" }}
                                <option value="{{$v['cate_id']}}" {{if condition="$v.cate_id eq $row['cate_id']"}}selected='selected' {{/if}}>{{$v['cate_name']}}</option>
                            {{/volist}}
                        </select>
                    </div>
                </div>
                
                <div class="hr-line-dashed"></div>
                <!-- <div class="form-group">
                    <label class="col-sm-2 control-label">关联题目</label>
                    <div class="col-sm-3">
                        <input type="text" name="question_id" class="form-control" value="{{$row.question_id}}"  />
                    </div>
                </div> -->
                <div class="form-group">
                    <label class="col-sm-2 control-label">上传视频</label>
                    <div class="col-sm-3">
                        {{notempty name="row['cdn_url']"}}
                        <video id="my-video" class="video-js" controls preload="auto" width="200" height="100">
                            <source src="{{:get_img_url('video',$row['cdn_url'])}}" type="video/mp4">
                        </video>
                        {{/notempty}}
                        <input type="file" id="oFile" accept="video/*" name="upload_video" onchange="FileChangeFn(event)"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">视频预览</label>
                    <div class="col-sm-3">
                        <div id="file_box"></div>
                        <div class="speed_box"><div id="speed">0%</div></div>
                        <label id="file_type"></label>
                        <button type="button" class="send_btn" onclick="UploadFileFn()">开始上传文件</button>
                    </div>
                </div>
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input type="hidden" name="video_id" value="{{$row.video_id}}">
                        <button type="submit" class="btn btn-primary">保存内容</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>
<!-- <script src="__PUBLIC__/js/jquery-2.2.4.min.js"></script> -->
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
<script type="text/javascript">
    //文件选择完毕时
    function FileChangeFn(event) {
        $('.opst_txt').text('重新选择文件');
        $('.send_btn').show();
        var event = event || window.event,
            dom = '',
            ofile = $("#oFile").get(0).files[0],
            otype = ofile.type,
            osize = ofile.size / 1054000,
            ourl = window.URL.createObjectURL(ofile); //文件临时地址
        $('#file_type').text("选择上传文件类型："+ ofile.type);
        $('#file_size').text("选择上传文件大小，共"+ osize.toFixed(2) +"MB。");
            
            console.log("文件类型："+ otype); //文件类型
            console.log("文件大小："+ osize); //文件大小

        if('video/mp4' == otype || 'video/avi' == otype || 'video/x-msvideo' == otype){
            //autoplay="autoplay" width="100%" height="100%" 
            dom = '<video id="video" controls="controls" src='+ ourl +'></video>';
        }
        if('audio/mp3' == otype || 'audio/wav' == otype){
            //autoplay="autoplay" 
            dom = '<audio id="audio" width="100%" height="100%" controls="controls" loop="loop" src='+ ourl +' ></audio>';
        }
        if('image/jpeg' == otype || 'image/png' == otype || 'image/gif' == otype){
            dom = '<img id="photo" width="100%" height="100%" alt="我是image图片文件" src='+ ourl +' title="" />';
        }   
        $('#file_box').html(dom);
    };
    
    //侦查附件上传情况 ,这个方法大概0.05-0.1秒执行一次
    function OnProgRess(event) {
        var event = event || window.event;
        //console.log(event);  //事件对象
        console.log("已经上传："+ event.loaded); //已经上传大小情况(已上传大小，上传完毕后就 等于 附件总大小)
        //console.log(event.total);  //附件总大小(固定不变)
        var loaded = Math.floor(100 * (event.loaded / event.total)); //已经上传的百分比  
        $("#speed").html(loaded + "%").css("width", loaded + "%");
    };

    //开始上传文件
    function UploadFileFn() {   
        $('.speed_box').show();
        var oFile = $("#oFile").get(0).files[0],//input file标签
            formData = new FormData();          //创建FormData对象
            xhr = $.ajaxSettings.xhr();         //创建并返回XMLHttpRequest对象的回调函数(jQuery中$.ajax中的方法)
        formData.append("upload_video", oFile);      //将上传name属性名(注意：一定要和 file元素中的name名相同)，和file元素追加到FormData对象中去
        
        $.ajax({
            type: "POST",
            url: "/Ajax/upload_video_to_local", //后端服务器上传地址
            // url: "/Ajax/upload_video_to_aliyun", //阿里云上传地址
            data: formData,         //formData数据
            cache:false,            //是否缓存
            async: true,            //是否异步执行
            processData: false,     // 是否处理发送的数据  (必须false才会避开jQuery对 formdata 的默认处理)
            contentType: false,     // 是否设置Content-Type请求头
            xhr: function() {
                if(OnProgRess && xhr.upload) {
                    xhr.upload.addEventListener("progress", OnProgRess, false);
                    return xhr;
                }
            },
            success: function (returndata) {
                $("#speed").html("上传成功");
                console.log("返回结果："+ returndata); 
                //alert(returndata);  
            },  
            error: function (returndata) {
                $("#speed").html("上传失败");
                console.log(returndata)
                alert('请正确配置后台服务！');  
            }
        });
    };
</script>
</body>
</html>





            

