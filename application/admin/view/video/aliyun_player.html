<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="x-ua-compatible" content="IE=edge" >
<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"/>
<title>Aliplayer Online Settings</title>
<link rel="stylesheet" href="https://g.alicdn.com/apsara-media-box/imp-web-player/2.30.3/skins/default/aliplayer-min.css" />
<script type="text/javascript" charset="utf-8" src="https://g.alicdn.com/apsara-media-box/imp-web-player/2.30.3/aliplayer-min.js"></script>
</head>
<body>
<div class="prism-player" id="player-con"></div>
<script>
var player = new Aliplayer({
  "id": "player-con",
  "source": "//player.alicdn.com/video/aliyunmedia.mp4",
  "width": "100%",
  "height": "500px",
  "autoplay": false,
  "isLive": false,
  "rePlay": false,
  "videoHeight": undefined,
  "isVBR": undefined,
  "preload": true,
  "controlBarVisibility": "click",
  "showBarTime": 5000,
  "useH5Prism": true
}, function (player) {
    console.log("The player is created");
  }
);
</script>
</body>