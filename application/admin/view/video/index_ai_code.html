<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>阿里云视频播放器</title>
    <link href="__PUBLIC__/aliyun-sdk/aliplayer-min.css" rel="stylesheet" /> 
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        .video-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .video-item {
            border: 1px solid #ccc;
            padding: 10px;
            width: 200px;
            text-align: center;
        }

        .play-btn {
            background-color: #007BFF;
            color: white;
            border: none;
            padding: 10px 15px;
            cursor: pointer;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
        }

        .modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 800px;
            position: relative;
        }

        .close {
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 24px;
            cursor: pointer;
        }

        #player-container {
            width: 100%;
            height: 500px;
        }
    </style>
</head>
<body>

<div class="video-list">
    <!-- 示例视频列表 -->
    <div class="video-item">
        <h3>视频1</h3>
        <button class="play-btn" onclick="playVideo('f0eec3f22c7871f09d191776b3ce0102')">播放</button>
    </div>
    <div class="video-item">
        <h3>视频2</h3>
        <button class="play-btn" onclick="playVideo('8075f0d32c8171f0b65c7fb2780c0102')">播放</button>
    </div>
</div>

<!-- 弹窗 -->
<div class="modal" id="videoModal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <div id="player-container"></div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

<!-- 自定义js -->
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
<!-- 引入阿里云播放器资源 -->
<!-- <script src="assets/aliplayer-min.js"></script> -->
<script src="__PUBLIC__/aliyun-sdk/aliplayer-min.js" charset="utf-8" type="text/javascript" ></script>
<script>
    let player;

    function playVideo(videoId) {
        // fetch(`get_playauth.php?video_id=${videoId}`)
        //     .then(response => response.json())
        //     .then(data => {
        //         if (data.playauth) {
        //         
        try{
            $.ajax({
                url: '/Aliyun/get_vod_playAuth',
                type: 'post',
                data: {
                    vid: videoId,
                },
                success: function(res) {
                    console.log('res:');
                    console.log(res);
                    if(res.status==200) {
                        let _body = res.data.body;
                        // document.getElementById('videoModal').style.display = 'block';
                        // initPlayer(vid, _body.playAuth); // 初始化播放器
                        // $('#videoModal').modal('show');
                        document.getElementById('videoModal').style.display = 'block';
                        console.log('playAuth:');
                        console.log(_body.playAuth);
                        initPlayer(_body.playAuth, videoId);
                    }else {
                        alert(res.info);
                    }
                },
                error: function() {
                    alert('网络波动，请稍后重试');
                }
            })
        }catch(err){
            alert('数据错误');
        }        
                    
        //         } else {
        //             alert('无法获取播放凭证');
        //         }
        //     })
        //     .catch(err => {
        //         console.error('获取播放凭证失败:', err);
        //         alert('播放失败，请重试');
        //     });
    }

    function closeModal() {
        document.getElementById('videoModal').style.display = 'none';
        if (player) {
            player.dispose(); // 销毁播放器
        }
    }

    function initPlayer(playauth, videoId) {
        player = new Aliplayer({
            id: 'player-container',
            width: '100%',
            height: '100%',
            playauth: playauth,
            videoId: videoId,
            autoplay: true,
            isLive: false,
            rePlay: false,
            // cover: 'https://your-cdn.com/cover.jpg', // 封面图
            skinLayout: [
                { type: 'bigPlayButton', align: 'center' },
                { type: 'progress', align: 'bottom' },
                { type: '弹幕', align: 'bottom' },
                { type: 'subtitle', align: 'bottom' },
                { type: 'control', align: 'bottom' }
            ]
        });
    }
</script>

</body>
</html>
