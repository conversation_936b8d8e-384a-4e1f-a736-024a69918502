<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
    <link href="__PUBLIC__/aliyun-sdk/aliplayer-min.css" rel="stylesheet" /> 
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>视频库</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('index')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">分类</span>
                                        <select name="cate_id" class="form-control">
                                            <option value="">请选择</option>
                                            {{volist name="cate_list" id="v" }}
                                                <option value="{{$v['cate_id']}}" {{if condition="$v.cate_id eq $filter['cate_id']"}}selected='selected' {{/if}}>{{$v['cate_name']}}</option>
                                            {{/volist}}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <input type="text" name="keywords" class="form-control" value="{{$filter['keywords']}}" placeholder="请输入视频名称">
                                </div>
                                <div class="col-sm-4 m-b">
                                    <div class="input-group">
                                        <input type="text" class="form-control calendar datepicker" name="start_time" value="{{$filter.start_time|date='Y-m-d'}}"  placeholder="创建开始时间" required/>
                                        <span class="input-group-addon b-lr-none"><i class="fa fa-calendar"></i></span>
                                        <input type="text" class="form-control calendar datepicker" name="end_time" value="{{$filter.end_time|date='Y-m-d'}}"  placeholder="创建结束时间" required/>
                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('index')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-sm-2 m-b clearfix">
                        <a class="btn btn-primary pull-right" href="{{:url('info')}}"><i class="fa fa-plus"></i> 添加</a>
                    </div>
                </div>

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>文件名</th>
                        <th>分类</th>
                        <th>视频文件</th>
                        <th>创建人</th>
                        <th>创建时间</th>
                        <th class="col-sm-2">操作</th>
                    </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    {{volist name="rows" id="v"}}
                        <tr>
                            <td class="text-center">{{:get_sequence($pagesize, $i)}}</td>
                            <td>{{$v.video_name}}</td>
                            <td>{{$v.cate_name}}</td>
                            <td class="text-center">
                                 <a href="javascript:;" class="view_video" data-url = "{{:get_img_url('video',$v['cdn_url'])}}" data-vid="{{$v.oss_video_id}}">查看视频</a>
                            </td>
                            <td>{{$v.operator_name}}</td>
                            <td>{{$v.create_time|date="Y-m-d H:i"}}</td>
                            <td>
                                <a class="btn btn-sm btn-success" href="{{:url('info',array('video_id'=>$v['video_id']))}}">编辑</a>
                                <a class="btn btn-sm btn-danger fn-confirm" href="javascript:;" data-link="{{:url('delete',array('video_id'=>$v['video_id']))}}">删除</a>
                            </td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>

                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>

            </div>
        </div>

        <!-- 触发弹窗的按钮 -->
        <!-- <button id="openModal">打开视频弹窗</button> -->

        <!-- 弹窗容器 -->
        <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-content">
                <span class="close" onclick="closeModal()">&times;</span>
                <div id="playerContainer"></div>
            </div>
        </div>

    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
    <script src="__PUBLIC__/aliyun-sdk/aliplayer-min.js" charset="utf-8" type="text/javascript" ></script>
    <script>
        const modal = document.getElementById('videoModal');
        const playerContainer = document.getElementById('playerContainer');

        // 初始化播放器
        function initPlayer(videoId, playAuth) {
          const player = new Aliplayer({
            id: 'playerContainer',
            width: '550px',
            height: '264px',
            autoplay: true,
            vid: videoId,
            playauth: playAuth, // 若需要播放凭证，可填写
            cover: 'https://example.com/cover.jpg', // 封面图片 URL
            isLive: false,
            rePlay: false,
            autoPlay: true,
            preload: true,
            skinLayout: [
              { name: 'bigPlayButton', align: 'center' },
              { name: 'progress', align: 'bottom' },
              { name: 'volume', align: 'right' },
              { name: 'fullscreen', align: 'right' }
            ]
          });

          // 播放器事件监听
          player.on('play', () => {
            console.log('视频开始播放');
          });

          player.on('pause', () => {
            console.log('视频暂停');
          });

          player.on('ended', () => {
            console.log('视频播放结束');
          });
        }

        $('.view_video').on('click', function(){
            try{
                let vid = $(this).attr('data-vid');
                $.ajax({
                    url: '/Aliyun/get_vod_playAuth',
                    type: 'post',
                    data: {
                        vid: vid,
                    },
                    success: function(res) {
                        console.log('res:');
                        console.log(res);
                        if(res.status==200) {
                            let _body = res.data.body;
                            // document.getElementById('videoModal').style.display = 'block';
                            initPlayer(vid, _body.playAuth); // 初始化播放器
                            $('#videoModal').modal('show');
                        }else {
                            _alert(res.info);
                        }
                    },
                    error: function() {
                        _alert('网络波动，请稍后重试');
                    }
                })
            }catch(err){
                _alert('数据错误');
            }
        });
    </script>
</body>
</html>
