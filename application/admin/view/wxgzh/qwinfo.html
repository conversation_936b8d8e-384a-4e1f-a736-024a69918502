<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

<div class="wrapper">
    <div class="ibox">
        <div class="ibox-title">

            <h5>企微信息</h5>
            <div class="ibox-tools">
                <a id="page-goback" href="/Wxgzh/index">
                    <i class="fa fa-arrow-left"></i> 后退
                </a>
                <a id="page-refresh" href="javascript:;">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
        </div>
        <div class="ibox-content">

            <form class="form-horizontal validate" method="post" action="" autocomplete="off" enctype="multipart/form-data">
                <div class="form-group">
                    <label class="col-sm-2 control-label">企微名称</label>
                    <div class="col-sm-3 m-b">
                       <input type="text" class="form-control" name="name" value="{{$row.name}}" required>
                    </div>
                    <label class="col-sm-2 control-label">企微id(corp_id)</label>
                    <div class="col-sm-3 m-b">
                       <input type="text" class="form-control" name="corp_id" value="{{$row.corp_id}}" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">应用ID(agent_id)</label>
                    <div class="col-sm-3 m-b">
                       <input type="text" class="form-control" name="agent_id" value="{{$row.agent_id}}" required>
                    </div>
                    <label class="col-sm-2 control-label">秘钥(secret)</label>
                    <div class="col-sm-3 m-b">
                       <input type="text" class="form-control" name="secret" value="{{$row.secret}}" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">启用</label>
                    <div class="col-sm-3">
                        <div class="radio radio-inline radio-success">
                            <input id="status-1" type="radio" name="status" value="1" {{if $row.status != 4}}checked="checked"{{/if}}/>
                            <label for="status-1">是</label>
                        </div>
                        <div class="radio radio-inline radio-danger">
                            <input id="status-0" type="radio" name="status" value="4" {{if $row.status == 4}}checked="checked"{{/if}}/>
                            <label for="status-0">否</label>
                        </div>
                    </div>
                </div>
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input type="hidden" name="id" value="{{$row.id}}">
                        <input type="hidden" name="gzh_id" value="{{$gzh_id}}">
                        <button type="submit" class="btn btn-primary">保存内容</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

<!-- 自定义js -->
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
</body>
</html>





            

