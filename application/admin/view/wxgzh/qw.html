<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>企微管理</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('qw')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-2 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">状态</span>
                                        <select name="status" class="form-control">
                                            <option value="" >全部</option>
                                            <option value="1" {{if $filter['status'] == '1'}} selected='selected'{{/if}}>启用</option>
                                            <option value="4" {{if $filter['status'] == '4'}} selected='selected'{{/if}}>禁用</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <input type="text" name="keywords" class="form-control" value="{{$filter['keywords']}}" placeholder="企微名称">
                                </div>
                                <div class="col-sm-2">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('qw')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-sm-2 m-b clearfix">
                        <a class="btn btn-primary pull-right" href="{{:url('qwinfo',array('gzh_id'=>$gzh_id))}}"><i class="fa fa-plus"></i> 添加</a>
                    </div>
                </div>

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>企微名称</th>
                        <th>企微id(corp_id)</th>
                        <th>应用ID(agent_id)</th>
                        <th>秘钥(secret)</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th class="col-sm-2">操作</th>
                    </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    {{volist name="rows" id="v"}}
                        <tr>
                            <td class="text-center">{{:get_sequence($pagesize, $i)}}</td>
                            <td>{{$v.name}}</td>
                            <td>{{$v.corp_id}}</td>
                            <td>{{$v.agent_id}}</td>
                            <td>{{$v.secret}}</td>
                            <td>
                                {{if condition="$v.status eq 1"}}
                                    <span class="text-navy">启用</span>
                                {{else /}}
                                    <span class="text-danger">禁用</span>
                                {{/if}}
                            </td>
                            <td>{{$v.create_time|date="Y-m-d H:i"}}</td>
                            <td>
                                <a class="btn btn-sm btn-success" href="{{:url('qwinfo',array('gzh_id'=>$gzh_id,'id'=>$v['id']))}}">编辑</a>
                            </td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>

                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>

            </div>
        </div>

        <!--经销商邀请链接弹窗-->
        <div id="agency-auth-window" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title">经销商邀请注册链接</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group">
                                <div class="col-sm-5 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">状态</span>
                                        <select name="type" class="form-control">
                                            <option value="1">微信</option>
                                            <option value="2">企微</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-5 m-b hide">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">企微</span>
                                        <select name="qw_list" class="form-control chosen">
                                            <option value="101">请选择</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="wxgzh_id" value="">
                        <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                         <button class="btn btn-primary fn-copy">创建邀请链接</button>
                    </div>
                </div>	
            </div>
        </div>

    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
    <script src="__PUBLIC__/js/clipboard.min.js?v={{$time}}"></script>
    <script>
        $(function(){
            $('.agency-auth').on('click', function(){
                $('#agency-auth-window input[name="wxgzh_id"]').val($(this).data('id'));
                $('#agency-auth-window').modal('show');
            });
            $('select[name="type"]').on('change', function(){
                if($(this).val() == 2){
                    $('select[name="qw_list"]').parent().parent().removeClass('hide');
                }else{
                    $('select[name="qw_list"]').parent().parent().addClass('hide');
                }
            });
            $('.fn-copy').on('click', function(){
                var wxgzh_id = $('#agency-auth-window input[name="wxgzh_id"]').val();
                var type = $('#agency-auth-window select[name="type"]').val();
                var qw_list = $('#agency-auth-window select[name="qw_list"]').val();
                var url = '{{:Config("oauth_dealer")}}?state=' + wxgzh_id;
                if(type == 2){
                    url += '_' + qw_list;
                }
                //url添加到剪切板
                var clipboard = new ClipboardJS('.fn-copy', {
                    container: document.getElementById('agency-auth-window'),
                    text: function() {
                        return url;
                    }
                });
                clipboard.on('success', function(e) {
                    _alert('创建并复制链接成功');
                });
                clipboard.on('error', function(e) {
                    _alert('复制失败');
                });
            });
            $('.fn-copy-user').on('click', function(){
                var user_url = '{{:Config("oauth_user")}}?state=' + $(this).data('id');
                var clipboard = new ClipboardJS('.fn-copy-user', {
                    text: function() {
                        return user_url;
                    }
                });
                clipboard.on('success', function(e) {
                    _alert('复制个人中心链接成功');
                });
                clipboard.on('error', function(e) {
                    _alert('复制个人中心链接失败');
                });
            });
        })
    </script>
</body>
</html>
