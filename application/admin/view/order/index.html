<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>充值记录表</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('index')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">单号</span>
                                        <input type="text" name="keyword" class="form-control" value="{{$filter.keyword}}" />
                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('index')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>订单号</th>
                        <th>主体</th>
                        <th>充值金额</th>
                        <th>手续费</th>
                        <th>支付金额(元)</th>
                        <th>操作人</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <!-- <th>最新余额</th> -->
                        <th class="col-sm-2">操作</th>
                    </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    {{volist name="rows" id="v"}}
                        <tr>
                            <td class="text-center">{{:get_sequence($pagesize, $i)}}</td>
                            <td>{{$v.order_id}}</td>
                            <td>{{notempty name="v.agency_name"}}{{$v.agency_name}}{{else/}}{{$v.org_name}}{{/notempty}}</td>
                            <td>{{$v.amount}}</td>
                            <td>{{$v.service_fee}}</td>
                            <td>{{$v.order_amount}}</td>
                            <td>{{$v.operator_name}}</td>
                            <td class="{{:get_order_status($v['order_status'], 'tag')}}">{{:get_order_status($v['order_status'])}}</td>
                            <td>{{$v.create_time|date="Y-m-d H:i:s"}}</td>
                            <td>
                                {{if condition="$v.order_status eq 1"}}
                                <a class="btn btn-sm btn-danger" data-id="{{$v.agency_id}}" href="{{:url('Order/close',array('order_id'=>$v['order_id']))}}">关闭</a>
                                <a class="btn btn-sm btn-info" href="{{:url('Order/doPay',array('order_id'=>$v['order_id']))}}">支付</a>
                                {{else/}}
                                    -
                                {{/if}}
                            </td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>

                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
</body>
</html>
