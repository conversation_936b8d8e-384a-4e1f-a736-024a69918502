<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>营期统计</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('period')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-4 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">训练营</span>
                                        <select name="group_id" class="form-control chosen">
                                            <option value="" >请选择</option>
                                            {{volist name="period_group" id="v" }}
                                                <option value="{{$v.group_id}}" {{if condition="$v['group_id'] eq $filter['group_id']"}}selected='selected' {{/if}}>{{$v.group_name}}</option>
                                            {{/volist}}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-4 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">营期</span>
                                        <select name="period_id" class="form-control chosen">
                                            <option value="" >请选择</option>
                                            {{volist name="periods" id="v" }}
                                                <option value="{{$v.period_id}}" {{if condition="$v['period_id'] eq $filter['period_id']"}}selected='selected' {{/if}}>{{$v.period_name}}</option>
                                            {{/volist}}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-4 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">公众号</span>
                                        <select name="wxgzh_id" class="form-control">
                                            <option value="" >请选择</option>
                                            {{volist name=":get_wxgzh_list()" id="v" }}
                                                <option value="{{$key}}" {{if condition="$key eq $filter['wxgzh_id']"}}selected='selected' {{/if}}>{{$v}}</option>
                                            {{/volist}}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-6 m-b">
                                    <div class="input-group">
                                        <input type="text" class="form-control calendar grouppicker" placeholder="查询开始日期" name="start_date" value="{{$filter.start_date}}" >
                                        <span class="input-group-addon b-lr-none"><i class="fa fa-calendar"></i></span>
                                        <input type="text" class="form-control calendar grouppicker" placeholder="查询结束日期" name="end_date" value="{{$filter.end_date}}" >
                                        <span class="input-group-btn"><button type="button" class="btn btn-gray day-report">本日</button></span>
                                        <span class="input-group-btn"><button type="button" class="btn btn-success week-report">本周</button></span>
                                        <span class="input-group-btn"><button type="button" class="btn btn-info month-report">本月</button></span>
                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('period')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>训练营名称</th>
                        <th>营期名称</th>
                        <th>经销商昵称</th>
                        <th>观看人数</th>
                        <th>完播人数</th>
                        <th>完播率</th>
                        <th>答题人数</th>
                        <th>正确人数</th>
                        <th>正确率</th>
                        <th class="col-sm-2">操作</th>
                    </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    {{volist name="rows" id="v"}}
                        <tr>
                            <td class="text-center">{{:get_sequence($pagesize, $i)}}</td>
                            <td>{{$v.group_name}}</td>
                            <td>{{$v.period_name}}</td>
                            <td>{{:base64_decode($v.agency_name)}}</td>
                            <td>{{$v.user_count}}</td>
                            <td>{{$v.finish_count}}</td>
                            <td>{{:round($v.finish_count/$v.user_count*100,2)}}%</td>
                            <td>{{$v.question_count}}</td>
                            <td>{{$v.correct_count}}</td>
                            <td>
                                {{gt name="$v.question_count" value="0"}}
                                    {{:round($v.correct_count/$v.question_count*100,2)}}%
                                {{else /}}
                                    0%
                                {{/gt}}
                            </td>
                            <td>
                                <!-- <a class="btn btn-sm btn-success" href="{{:url('period_info',array('group_id'=>$v['group_id']))}}">营期分析</a> -->
                                <a class="btn btn-sm btn-success" href="javascript:;">-</a>
                            </td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>

                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
    <script>
        $(function(){
            var $from = $('[name=start_date]'), $to = $('[name=end_date]');

            $('.month-report').on('click', function(){
                $from.val(getCurrentMonthFirst().Format('yyyy-MM-dd'));
                $to.val(getCurrentMonthLast().Format('yyyy-MM-dd'));
            });
            $('.day-report').on('click', function(){
                var date = new Date();
                var currentDay = date.toLocaleDateString();
                var reg = new RegExp("/","g");
                $from.val(currentDay.replace(reg,"-"));
                $to.val(currentDay.replace(reg,"-"));
            });
            $('.week-report').on('click', function(){
                var weekRange = getWeekRange();
                $from.val(weekRange.start.Format('yyyy-MM-dd'));
                $to.val(weekRange.end.Format('yyyy-MM-dd'));
            });
            function getWeekRange() {
                var now = new Date();
                var day = now.getDay(); // 获取今天是周几，0（周日）到6（周六）
                var diff = now.getDate() - day + (day == 0 ? -6:1); // 获取本周一的日期
                var monday = new Date(now.setDate(diff));
                var sunday = new Date(monday);
                sunday.setDate(sunday.getDate() + 6); // 将周一的日期加上6天得到周日
                return {
                    start: monday,
                    end: sunday
                };
            }
        })
    </script>
</body>
</html>
