<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="renderer" content="webkit" />
    <title>资源销售平台</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <link rel="SHORTCUT ICON" href="/favicon.ico" />
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
    <link href="__PUBLIC__/css/pay.css" rel="stylesheet" />
</head>

<body>
    <div id="main" class="toolPay">
        <h1>支付信息</h1>
        <div class="order_msg">
            <div class="head">
                <p class="explain moneyColor">支付金额</p>
                <p class="jine moneyColor">{{$order['order_amount']|number_format=2}}元</p>
            </div>
            <div class="body">
                <p class="msg_item">订单编号：{{$order['order_id']}}</p>
                <p class="msg_item">产品信息：充值中心-会员充值</p>
                <p class="msg_item">支付方式：<span><img src="/static/img/wechatPay.png" alt="微信支付" /></span></p>
                <div class="line"></div>
                <!-- <br> -->
                <div id="wechart" class="qrcode">
                    <img class="code" src="/Tool/qrcode?content={{$resp['code_url']}}&size=8" alt="" />
                    <div>请扫二维码支付</div>
                </div>
                <div class="line"></div>
                <br>
                <div class="col-sm-2 col-sm-offset-2">
                    <input type="hidden" name="order_id" value="{{$order.order_id}}">
                    <a id="page-refresh" href="javascript:;" class="btn btn-sm btn-info" style="color:white;">
                        <i class="fa fa-refresh"></i> 刷新状态
                    </a>
                </div>

                <div class="col-sm-2 col-sm-offset-3">
                    <a id="page-refresh" href="javascript:;" class="btn btn-sm btn-danger fn-confirm" data-link="{{:url('Order/close',array('order_id'=>$order['order_id']))}}" style="color:white;">
                        关闭订单
                    </a>
                    <!-- <a class="btn btn-sm btn-danger fn-confirm" href="javascript:;" data-link="{{:url('delete',array('id'=>$v['id']))}}">解除注册</a> -->
                </div>
            </div>
        </div>
    </div>
</body>
<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
<script>
$(function () {
    'use strict'
    var PI = parseInt
    var Time = 5000, TimerID
    var oid = $('input[name="order_id"]').val()

    var checkTimer = function () {
        TimerID = setInterval(OnTimer, Time)
    }

    var OnTimer = function () {
        $.ajax({
            type: 'POST',
            data: 'oid=' + oid,
            url: '/Ajax/payment_status',
            dataType: 'JSON',
            beforeSend: function () {},
            success: function (json) {
                if (PI(json.data) === 5) {
                    window.location.href = '/Order/index'
                    clearInterval(TimerID)
                }
            },
            error: function () {}
        })
    }

    checkTimer()

    var wechart = $('#wechart'), alipay = $('#alipay');
    $('input:radio[name="paytype"]').change(function(e) {
        var type = $('input:radio[name="paytype"]:checked').val();
        if(type=='wechart') {
          wechart.show();
          alipay.hide();
        }else {
          wechart.hide();
          alipay.show();
        }
    });
})
</script>
</html>
