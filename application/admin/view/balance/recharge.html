<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

<div class="wrapper">
    <div class="ibox">
        <div class="ibox-title">

            <h5>充值管理</h5>
            <div class="ibox-tools">
                <a id="page-goback" href="{{:url('index')}}">
                    <i class="fa fa-arrow-left"></i> 后退
                </a>
                <a id="page-refresh" href="javascript:;">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
        </div>
        <div class="ibox-content">

            <form class="form-horizontal validate" method="post" action="" autocomplete="off">

                <div class="form-group">
                    <label for="inputPid" class="col-sm-2 control-label">主体信息</label>
                    <div class="col-sm-3">
                        <input type="text" name="name" class="form-control" value="{{$model.name}}" disabled />
                        </select>
                    </div>
                </div>
                <!-- <div class="form-group">
                    <label class="col-sm-2 control-label">所属公众号</label>
                    <div class="col-sm-3">
                        <input type="text" name="gzh_name" class="form-control" value="{{$row.gzh_name}}" disabled />
                    </div>
                </div> -->
                <div class="form-group">
                    <label class="col-sm-2 control-label">充值金额</label>
                    <div class="col-sm-3">
                        <input type="text" id="amount" name="amount" class="form-control" value="" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">手续费【1.00%】</label>
                    <div class="col-sm-3">
                        <input type="text" id="service_fee" name="service_fee" class="form-control" value="" readonly />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label text-danger">支付金额</label>
                    <div class="col-sm-3">
                        <input type="text" id="order_amount" name="order_amount" class="form-control" value="" disabled />
                    </div>
                </div>
                <div class="hr-line-dashed"></div>
                <div class="form-group">
                    <div class="col-sm-4 col-sm-offset-2">
                        <input type="hidden" name="agency_id" value="{{$model.agency_id}}">
                        <input type="hidden" name="pay_obj" value="{{$pay_obj}}" />
                        <input type="hidden" name="form_token" value="{{$form_token}}" />
                        <button type="submit" class="btn btn-primary">支付</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 全局js -->
<script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
<script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>
<!-- 自定义js -->
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
<script type="text/javascript">
    $(document).ready(function(){
        $('#amount').on('input', function() {
            var amount = $(this).val(); // 获取当前输入框的值：充值金额
            var tmp = amount / 100;
            var fee = tmp.toFixed(2);
            var total = parseFloat(amount) + parseFloat(fee);
            console.log('fee:'+fee);
            console.log('total:'+total);
            $('#service_fee').val(fee); // 将input1的值设置给input2：手续费
            $('#order_amount').val(total.toFixed(2)); // 订单金额
        });
    });
</script>
</body>
</html>