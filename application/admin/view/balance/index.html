<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>经销商余额表</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('index')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">经销商名称</span>
                                        <input type="text" name="keyword" class="form-control" value="{{$filter.keyword}}" />
                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('index')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-sm-2 m-b clearfix">
                        <a class="btn btn-primary pull-right" href="{{:url('info')}}"><i class="fa fa-plus"></i> 添加分组</a>
                    </div>
                </div>

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>公众号</th>
                        <th>经销商</th>
                        <th>经销商状态</th>
                        <th>账号余额(元)</th>
                        <th>预估可用天数</th>
                        <th class="col-sm-2">操作</th>
                    </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    {{volist name="rows" id="v"}}
                        <tr>
                            <td class="text-center">{{:get_sequence($pagesize, $i)}}</td>
                            <td>{{$v.gzh_name}}</td>
                            <td>{{:base64_decode($v.agency_name)}}</td>
                            <td class="{{:get_agency_status($v['status'], 'tag')}}">{{:get_agency_status($v.status)}}</td>
                            <td>{{$v.balance}}</td>
                            <td>-</td>
                            <td>
                                <a class="btn btn-sm btn-info" data-id="{{$v.agency_id}}" href="{{:url('recharge',array('agency_id'=>$v['agency_id'], 'pay_obj'=>3))}}">充值</a>
                                <a class="btn btn-sm btn-warning" href="{{:url('Balance/bill',array('agency_id'=>$v['agency_id']))}}">充值记录</a>
                                <a class="btn btn-sm btn-warning" href="{{:url('Balance/consume',array('agency_id'=>$v['agency_id']))}}">消费记录</a>
                            </td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>

                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
</body>
</html>
