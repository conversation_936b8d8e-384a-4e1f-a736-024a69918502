<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>余额变更明细表</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('index')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">关键词</span>
                                        <input type="text" name="keyword" class="form-control" value="{{$filter.keyword}}" />
                                        <!-- <select name="agency_id" class="form-control chosen">
                                            <option value="" >请选择</option>
                                            {{volist name=":getAgencyList($org_id, 9)" id="v" }}
                                                <option value="{{$key}}" {{eq name="$key" value="$filter['agency_id']"}}selected="selected"{{/eq}}>{{:base64_decode($v)}}</option>
                                            {{/volist}}
                                        </select> -->
                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('index')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>平台</th>
                        <th>经销商</th>
                        <th>消费前金额</th>
                        <th>消费金额(元)</th>
                        <th>消费后金额</th>
                        <th>说明</th>
                        <th>操作人</th>
                        <th>操作时间</th>
                    </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    {{volist name="rows" id="v"}}
                        <tr>
                            <td class="text-center">{{:get_sequence($pagesize, $i)}}</td>
                            <td>{{$v.org_name}}</td>
                            <td>{{$v.agency_name}}</td>
                            <td>{{$v.balance_before}}</td>
                            <td>{{$v.amount}}</td>
                            <td>{{$v.balance_after}}</td>
                            <td>{{$v.remark}}</td>
                            <td>{{$v.operator_name}}</td>
                            <td>{{$v.create_time|date="Y-m-d H:i:s"}}</td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>

                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
</body>
</html>
