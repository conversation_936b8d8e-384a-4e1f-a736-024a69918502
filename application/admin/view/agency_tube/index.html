<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:Config('app_name')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">

    <div class="wrapper">

        <div class="ibox">
            <div class="ibox-title">
                <h5>群管管理</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-10 m-b">
                        <form method="get" action="{{:url('index')}}" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">公众号</span>
                                        <select name="wxgzh_id" class="form-control">
                                            <option value="" >请选择</option>
                                            {{volist name=":get_wxgzh_list()" id="v" }}
                                                <option value="{{$key}}" {{if condition="$key eq $filter['wxgzh_id']"}}selected='selected' {{/if}}>{{$v}}</option>
                                            {{/volist}}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">类型</span>
                                        <select name="type" class="form-control">
                                            <option value="" >请选择</option>
                                            <option value="1" {{if condition="$filter['type'] eq 1"}}selected='selected' {{/if}}>企微</option>
                                            <option value="2" {{if condition="$filter['type'] eq 2"}}selected='selected' {{/if}}>微信</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <span class="input-group-addon gray-bg">经销商</span>
                                        <select name="agency_id" class="form-control chosen">
                                            <option value="" >请选择</option>
                                            {{volist name="agency_list" id="v" }}
                                                <option value="{{$key}}" {{eq name="$key" value="$filter['agency_id']"}}selected="selected"{{/eq}}>{{:base64_decode($v)}}</option>
                                            {{/volist}}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-2">
                                    <input type="hidden" name="status" value="{{$filter['status']}}">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:url('index',array('status'=>$filter['status']))}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                {{notempty name="rows"}}
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>头像</th>
                        <th>姓名</th>
                        <th>手机号</th>
                        <th>公众号名称</th>
                        <th>经销商</th>
                        <th>微信昵称</th>
                        <th>登录类型</th>
                        <th>最近登录时间</th>
                        <th>企微主体</th>
                        <th class="col-sm-2">操作</th>
                    </tr>
                    </thead>
                    <tbody class="tooltip-fn">
                    {{volist name="rows" id="v"}}
                        <tr>
                            <td class="text-center">{{:get_sequence($pagesize, $i)}}</td>
                            <td class="text-center"><img src="{{$v.avatar}}" width="25" height="25" /></td>
                            <td class="text-center">{{:base64_decode($v.tube_name)}}</td>
                            <td>{{$v.tel?$v.tel:'-'}}</td>
                            <td>{{$v.gzh_name}}</td>
                            <td>{{:base64_decode($v.agency_name)}}</td>
                            <td>{{:base64_decode($v.nickname)}}</td>
                            <td>
                                {{if condition="$v.type eq 1"}}
                                    <span class="text-navy">企微</span>
                                {{else /}}
                                    <span class="text-success">微信</span>
                                {{/if}}
                            </td>
                            <td>
                                {{notempty name="v.last_visit"}}
                                    {{$v.last_visit|date="Y-m-d H:i"}}
                                {{else/}}
                                    -
                                {{/notempty}}
                            </td>
                            <td>{{$v.qw_name?$v.qw_name:'-'}}</td>
                            <td>
                                    <a class="btn btn-sm btn-primary edit" data-id="{{$v.tube_id}}" data-tel="{{$v.tel}}" data-name="{{:base64_decode($v.tube_name)}}">修改</a>
                                    <a class="btn btn-sm btn-danger fn-confirm" href="javascript:;" data-link="{{:url('delete',array('tube_id'=>$v['tube_id']))}}">解除注册</a>
                            </td>
                        </tr>
                    {{/volist}}
                    </tbody>
                </table>

                <div class="row">
                    <div class="page-box">
                        {{$page|raw}}
                    </div>
                </div>
                {{else/}}
                <h1 class="wrapper text-center"> 
                    <i class="fa fa-4x fa-sitemap"></i>
                    <p class="m-t">暂无数据</p>
                </h1>
                {{/notempty}}

            </div>
        </div>

        <!--修改弹窗-->
        <div id="agency-edit-window" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title">群管信息</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group">
                                <div class="col-sm-2 control-label">姓名:</div>
                                <div class="col-sm-6">
                                    <input class="form-control address" name='tube_name'></input>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-2 control-label">手机号:</div>
                                <div class="col-sm-6">
                                    <input class="form-control address" name='tel' required></input>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="tube_id" value="">
                        <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                        <button type="submit" class="btn btn-primary" id='Edit-Save'>保存</button>
                    </div>
                </div>	
            </div>
        </div>

    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
    <script>
        $(function(){
            //弹窗
            $('.edit').on('click', function(){
                $('#agency-edit-window input[name="tube_name"]').val($(this).data('name'));
                $('#agency-edit-window input[name="tel"]').val($(this).data('tel'));
                $('#agency-edit-window input[name="tube_id"]').val($(this).data('id'));
                $('#agency-edit-window').modal('show');
            });
            //弹窗保存
            $('#Edit-Save').on('click', function(){
                var tube_id = $('#agency-edit-window input[name="tube_id"]').val();
                var tube_name = $('#agency-edit-window input[name="tube_name"]').val();
                var tel = $('#agency-edit-window input[name="tel"]').val();
                $.ajax({
                    url: '/ajax/edit_agency_tube',
                    type: 'post',
                    data: {
                        tube_id:tube_id,
                        tube_name:tube_name,
                        tel:tel
                    },
                    success: function(res) {
                        if(res.status==200) {
                            window.location.reload();
                        }else {
                            _alert(res.info);
                        }
                    },
                    error: function() {
                        _alert('网络波动，请稍后重试');
                    }
                })
            });
        })
    </script>
</body>
</html>
