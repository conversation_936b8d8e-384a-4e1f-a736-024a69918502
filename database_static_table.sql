-- 群管统计数据表
CREATE TABLE IF NOT EXISTS `ksd_org_tube_static` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tube_id` int(11) NOT NULL COMMENT '群管ID',
  `tube_name` varchar(100) NOT NULL DEFAULT '' COMMENT '群管名称',
  `agency_id` int(11) NOT NULL DEFAULT '0' COMMENT '经销商ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `play_count` int(11) NOT NULL DEFAULT '0' COMMENT '播放人数',
  `finish_count` int(11) NOT NULL DEFAULT '0' COMMENT '完播人数',
  `answer_count` int(11) NOT NULL DEFAULT '0' COMMENT '答题人数',
  `correct_count` int(11) NOT NULL DEFAULT '0' COMMENT '答对人数',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tube_date` (`tube_id`, `stat_date`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群管统计数据表';
