-- 数据统计表（基于 date_statistics 表结构）
CREATE TABLE IF NOT EXISTS `ksd_date_statistics` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `year` smallint(6) NOT NULL DEFAULT '0' COMMENT '年',
  `month` smallint(6) NOT NULL DEFAULT '0' COMMENT '月',
  `day` smallint(6) NOT NULL DEFAULT '0' COMMENT '日',
  `hour` smallint(6) NOT NULL DEFAULT '0' COMMENT '小时',
  `org_id` int(11) NOT NULL DEFAULT '0' COMMENT '商户ID',
  `org_name` varchar(128) DEFAULT '' COMMENT '商户NAME',
  `gzh_id` int(11) NOT NULL DEFAULT '0' COMMENT '公众号ID',
  `gzh_name` varchar(128) DEFAULT '' COMMENT '公众号NAME',
  `agency_id` int(11) NOT NULL DEFAULT '0' COMMENT '经销商ID',
  `agency_name` varchar(128) DEFAULT '' COMMENT '经销商NAME',
  `tube_id` int(11) NOT NULL DEFAULT '0' COMMENT '群管ID',
  `tube_name` varchar(128) DEFAULT '' COMMENT '群管NAME',
  `watch_count` int(11) DEFAULT '0' COMMENT '观看人数',
  `finish_count` int(11) DEFAULT '0' COMMENT '完播人数',
  `answer_count` int(11) DEFAULT '0' COMMENT '答题人数',
  `correct_count` int(11) DEFAULT '0' COMMENT '答题正确人数',
  `create_time` int(11) DEFAULT '0' COMMENT '添加时间',
  `update_time` int(11) DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_tube` (`year`, `month`, `day`, `hour`, `tube_id`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_tube_id` (`tube_id`),
  KEY `idx_gzh_id` (`gzh_id`),
  KEY `idx_date` (`year`, `month`, `day`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8 COMMENT='数据统计';
