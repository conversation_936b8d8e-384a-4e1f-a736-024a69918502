# DoStatic 群管统计命令使用说明

## 功能概述

DoStatic 是一个用于统计群管相关数据的命令行工具，基于 `org_user_watch` 表的数据，按群管 `tube_id` 进行分组统计以下指标：

- **播放人数**: 观看视频的用户数量（去重）
- **完播人数**: 完成视频播放的用户数量（is_finish=1）
- **答题人数**: 参与答题的用户数量（answer_id>0）
- **答对人数**: 答题正确的用户数量（is_correct=1）

## 安装步骤

### 1. 创建数据库表

执行以下 SQL 语句创建统计数据表：

```sql
-- 执行 database_static_table.sql 中的 SQL 语句
```

### 2. 配置文件

配置文件位于 `config/static.php`，包含以下配置项：

- `static_config`: 基础统计配置
- `static_fields`: 统计字段映射
- `db_fields`: 数据库查询条件配置
- `command_config`: 命令执行配置
- `notification`: 通知配置

## 命令使用方法

### 基本语法

```bash
php think dostatic [选项]
```

### 可用选项

| 选项 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--tube_id` | `-t` | 指定群管ID进行统计 | `-t 123` |
| `--date` | `-d` | 指定统计日期(Y-m-d格式) | `-d 2024-01-15` |
| `--force` | `-f` | 强制重新统计已存在的数据 | `-f` |
| `--clean` | `-c` | 清理过期的统计数据 | `-c` |
| `--range` | `-r` | 统计日期范围 | `-r 2024-01-01,2024-01-31` |

### 使用示例

#### 1. 统计今天所有群管的数据
```bash
php think dostatic
```

#### 2. 统计指定日期的数据
```bash
php think dostatic -d 2024-01-15
```

#### 3. 统计指定群管的数据
```bash
php think dostatic -t 123 -d 2024-01-15
```

#### 4. 强制重新统计（覆盖已存在的数据）
```bash
php think dostatic -d 2024-01-15 -f
```

#### 5. 统计日期范围内的数据
```bash
php think dostatic -r 2024-01-01,2024-01-31
```

#### 6. 清理过期数据
```bash
php think dostatic -c
```

## 输出说明

命令执行时会输出以下信息：

- 开始执行时间和统计日期
- 找到的群管数量
- 每个群管的统计结果
- 最终的成功/失败统计

示例输出：
```
开始执行群管统计任务...
统计日期: 2024-01-15
找到 5 个群管需要统计
群管 测试群管1(ID:123) 统计完成
群管 测试群管2(ID:124) 统计完成
群管 测试群管3(ID:125) 统计跳过（数据已存在）
日期 2024-01-15 统计完成！成功: 2, 失败/跳过: 1
所有统计任务完成！
```

## 数据表结构

统计结果保存在 `ksd_org_tube_static` 表中，包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 主键ID |
| tube_id | int | 群管ID |
| tube_name | varchar | 群管名称 |
| agency_id | int | 经销商ID |
| stat_date | date | 统计日期 |
| play_count | int | 播放人数 |
| finish_count | int | 完播人数 |
| answer_count | int | 答题人数 |
| correct_count | int | 答对人数 |
| create_time | int | 创建时间 |
| update_time | int | 更新时间 |

## 定时任务配置

建议配置定时任务每天自动执行统计：

```bash
# 每天凌晨2点执行昨天的统计
0 2 * * * cd /path/to/project && php think dostatic -d $(date -d "yesterday" +\%Y-\%m-\%d)

# 每周日凌晨3点清理过期数据
0 3 * * 0 cd /path/to/project && php think dostatic -c
```

## 注意事项

1. **数据唯一性**: 同一群管同一日期的数据具有唯一性约束
2. **性能考虑**: 大量数据统计时建议在低峰期执行
3. **日志记录**: 执行过程中的错误会记录到系统日志中
4. **数据清理**: 定期清理过期数据以节省存储空间
5. **权限要求**: 确保命令执行用户有数据库读写权限

## 故障排除

### 常见错误及解决方案

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务正常运行

2. **表不存在错误**
   - 执行 SQL 脚本创建统计表

3. **日期格式错误**
   - 确保日期格式为 Y-m-d（如：2024-01-15）

4. **权限不足**
   - 检查文件和目录权限
   - 确认数据库用户权限

## 技术支持

如遇到问题，请检查以下日志文件：
- 应用日志：`runtime/log/`
- 命令执行日志：通过配置文件中的日志设置查看
