(()=>{var t={52:(t,A,e)=>{"use strict";function n(t){let A=document.createElement("div");return A.innerHTML=t,A.childNodes[0]}function i(t,A,e){var n=new Date;n.setTime(n.getTime()+24*e*60*60*1e3);var i="expires="+n.toGMTString();document.cookie=t+"="+escape(A)+"; "+i}function o(t){return t instanceof Element&&t.nodeType===Node.ELEMENT_NODE}e.d(A,{DR:()=>i,a8:()=>n,vq:()=>o})},3525:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,'.abp{position:relative}.abp .container{-webkit-transform:matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);transform:matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);position:absolute;display:block;overflow:hidden;margin:0;border:0;top:0;left:0;bottom:0;right:0;z-index:9999;touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.abp .container .cmt{-webkit-transform:matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);transform:matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);-webkit-transform-origin:0% 0%;-ms-transform-origin:0% 0%;transform-origin:0% 0%;position:absolute;padding:3px 0 0 0;margin:0;color:#fff;font-family:SimHei, SimSun, Heiti, "MS Mincho", "Meiryo", "Microsoft YaHei", monospace;font-size:25px;text-decoration:none;text-shadow:-1px 0 black, 0 1px black, 1px 0 black, 0 -1px black;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;text-size-adjust:none;line-height:100%;letter-spacing:0;word-break:keep-all;white-space:pre}.abp .container .cmt.noshadow{text-shadow:none}.abp .container .cmt.rshadow{text-shadow:-1px 0 white, 0 1px white, 1px 0 white, 0 -1px white}@font-face{font-family:"\\9ED1\\4F53";src:local("SimHei")}@font-face{font-family:"\\5B8B\\4F53";src:local("SimSun")}@font-face{font-family:"\\534E\\6587\\6977\\4F53";src:local("SimKai")}@font-face{font-family:"\\5E7C\\5706";src:local("YouYuan")}@font-face{font-family:"\\5FAE\\8F6F\\96C5\\9ED1";src:local("Microsoft YaHei")}\n',""]);const r=a},807:(t,A,e)=>{"use strict";e.d(A,{A:()=>f});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o),r=e(4417),l=e.n(r),s=new URL(e(5029),e.b),c=new URL(e(396),e.b),d=new URL(e(4427),e.b),p=new URL(e(7214),e.b),m=a()(i()),h=l()(s),u=l()(s,{hash:"#iefix"}),y=l()(c),g=l()(d),M=l()(p,{hash:"#iconfont"});m.push([t.id,`@font-face{font-family:"iconfont";src:url(${h});src:url(${u}) format("embedded-opentype"),url(${y}) format("woff"),url(${g}) format("truetype"),url(${M}) format("svg")}.iconfont{font-family:"iconfont" !important;font-size:16px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.icon-list:before{content:"\\e643"}.icon-skipnext:before{content:"\\ea9e"}.icon-skip-previous:before{content:"\\e6d3"}.icon-close:before{content:"\\e616"}.icon-player-rotate-left:before{content:"\\e658"}.icon-player-rotate-right:before{content:"\\e659"}.icon-player-switch:before{content:"\\e781"}.icon-danmu-close:before{content:"\\e696"}.icon-danmu-open:before{content:"\\e697"}.icon-player-play:before{content:"\\e641"}body{font-family:"Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif}.vip-join{color:#00c1de}.vip_limit_content{display:flex;width:100%;height:100%;flex-wrap:wrap;justify-content:center;align-items:center}.vip_limit_content .title{font-size:18px;line-height:36px;color:#fff;text-align:center;width:100%}.vip_limit_button_box{text-align:center;white-space:nowrap;overflow:hidden;width:100%}.vip_limit_btn{display:inline-block;min-width:100px;position:relative;background:#f60;padding:0 35px;margin:0px 5px 20px 5px;border-radius:38px;font-size:18px;line-height:38px;color:#623A0C;text-align:center;background-image:linear-gradient(-135deg, #FBE8A8 0%, #F8E7AC 15%, #E2C078 100%);cursor:pointer}.vip_limit_close{text-align:center}.vip_limit_close span{display:inline-block;width:40px;height:40px;line-height:36px;background:rgba(165,165,165,0.54);border-radius:50%;font-size:24px;cursor:pointer}\n`,""]);const f=m},631:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".aliplayer-danmuku{position:absolute;top:0;left:0;width:100%;height:100%}.aliplayer-danmuku.abp{position:absolute}.aliplayer-danmuku.abp .container{z-index:0}.aliplayer-danmuku .danmu{position:absolute;width:100%;height:100%}.ali-danmuku-control{float:right;color:#fff;margin-right:5px;position:relative;display:table-cell;margin-top:8px}.ali-danmuku-control .iconfont{font-size:35px;cursor:pointer;vertical-align:middle;user-select:none;line-height:initial}.ali-danmuku-control .iconfont.icon-danmu-close{display:inline-block}.ali-danmuku-control .player-tooltip.close,.ali-danmuku-control .player-tooltip.open{right:-12px}@media (min-width: 768px){.ali-danmuku-control .icon-danmu-close:hover+.player-tooltip{display:block}.ali-danmuku-control .icon-danmu-open:hover+.player-tooltip{display:block}}.ali-danmuku-control .ali-danmu-input-wrap{width:200px;display:none}.ali-danmu-input{position:relative;width:100%;display:inline-block}.ali-danmu-input input{display:inline-block;width:100%;padding-right:40px;box-sizing:border-box;padding:5px 40px 5px 5px;background-color:rgba(130,132,138,0.4);border:1px solid #c0c4cc;font-size:14px;color:#fff;border-radius:3px}.ali-danmu-input input:focus{outline:none;border-color:#0f84fd}.ali-danmu-input .danmu-input-enter{position:absolute;right:0;top:0;border:1px solid;color:#fff;background-color:#0f84fd;border-color:#0f84fd;font-size:12px;padding:6px 7px;display:inline-block;height:28px;border-radius:0 3px 3px 0;cursor:pointer}.ali-danmu-input .danmu-input-enter:focus{outline:none}.ali-danmu-input .danmu-input-enter:hover{background-color:#288df5;border-color:#288df5}\n",""]);const r=a},2049:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".bullet-screen{position:absolute;white-space:nowrap;animation:bullet 10s linear infinite}@keyframes bullet{from{left:100%}to{left:0%;transform:translateX(-100%)}}\n",""]);const r=a},1209:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".player-hidden{display:none !important}.caption-components{float:right;color:#fff;height:35px;position:relative;box-sizing:border-box;margin-top:5px}.current-caption{display:flex;height:100%;align-items:center;justify-content:center;width:100px;cursor:pointer}.caption-list{position:absolute;bottom:46px;display:none;padding:0;margin:0;list-style:none;height:150px;width:100px;overflow-y:scroll}.caption-list li{text-align:center;width:100px;line-height:30px;background-color:rgba(0,0,0,0.6);cursor:pointer}.caption-list li.current{color:#00c1de}.caption-list li+li{border-top:1px solid rgba(78,78,78,0.3)}.caption-list li:hover{background-color:rgba(0,0,0,0.5)}@media (max-width: 768px){.current-caption{width:40px}.caption-list li{width:43px}}.prism-player .prism-info-display .switchimg{color:#fff}\n",""]);const r=a},6730:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".many-video-ad-component{width:100%;height:100%;position:absolute;top:0;left:0;z-index:2147483647;background-color:#000;font-size:12px}.many-video-ad-component #many-video-ad-content{width:100%;height:100%}.many-video-ad-component .many-video-ad-link{width:100%;height:100%;display:block;position:absolute;top:0;left:0}.many-video-ad-component .many-video-ad-close{background-color:rgba(59,59,59,0.85);position:absolute;top:15px;right:20px;color:#fff;line-height:26px;padding:0 10px;user-select:none}.many-video-ad-component .many-video-ad-close #many-video-ad-duration{color:#00c1de;vertical-align:top;font-size:14px;position:relative;display:inline-block;text-align:right}.many-video-ad-component .many-video-ad-close #many-video-ad-duration::after{background-color:#fff;position:absolute;content:'';right:-7px;top:8px;bottom:8px;width:1px}.many-video-ad-component .many-video-ad-close label{cursor:pointer;display:inline-block}.many-video-ad-component .many-video-ad-close .icon-close{font-size:12px;vertical-align:top}@media (min-width: 768px){.many-video-ad-component .many-video-ad-close .icon-close:hover{color:#00c1de}}.many-video-ad-component .many-video-ad-close .many-video-ad-close-text{padding:0 5px 0 10px}.many-video-ad-component .many-video-ad-detail{position:absolute;right:35px;bottom:30px;background-color:rgba(0,222,255,0.85);color:#fff;padding:8px 12px;user-select:none;cursor:pointer;transition:background-color .15s}@media (min-width: 768px){.many-video-ad-component .many-video-ad-detail:hover{background-color:#00deff}}.many-video-ad-component .autoplay-many-video-ad{position:absolute;color:#fff;top:50%;left:50%;text-align:center;padding:10px;border-radius:5px;background-color:rgba(144,147,153,0.85);transform:translate(-50%, -50%);display:none}.many-video-ad-component .autoplay-many-video-ad i{font-size:42px;margin-bottom:7px;display:inline-block;cursor:pointer}.many-video-ad-component .autoplay-many-video-ad i+span{margin-bottom:5px}.many-video-ad-component .autoplay-many-video-ad span{display:block}.controlbar-element-hidden{display:none !important}\n",""]);const r=a},4078:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".memory-play-wrap{position:absolute;right:10px;bottom:55px}.memory-play-wrap .memory-play{padding:13px 20px;background-color:#191919;background-color:rgba(25,25,25,0.88);border-radius:4px;color:#fff;font-size:14px;user-select:none}.memory-play-wrap .memory-play .icon-close{transition:color .3s;font-size:14px;cursor:pointer}@media (min-width: 768px){.memory-play-wrap .memory-play .icon-close:hover{color:#00c1de}}.memory-play-wrap .memory-play .play-jump{color:#00c1de;padding:5px;border-radius:3px;cursor:pointer}@media (min-width: 768px){.memory-play-wrap .memory-play .play-jump:hover{background-color:rgba(255,255,255,0.2)}}\n",""]);const r=a},2190:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".pause-ad{width:60%;height:50%;background:#000;top:50%;left:50%;transform:translate(-50%, -50%);position:absolute;display:none}.pause-ad .btn-close{position:absolute;top:5px;right:5px;background-color:#333;width:2px;height:15px;padding:2px 8px;cursor:pointer}.pause-ad .btn-close i{background-color:#fff;width:2px;height:15px;position:absolute}.pause-ad .btn-close .split-left{transform:rotate(-45deg)}.pause-ad .btn-close .split-right{transform:rotate(45deg)}.pause-ad .ad-text{position:absolute;left:5px;top:5px;background-color:#333;color:#fff;padding:3px 5px;font-size:14px}.pause-ad .ad-content{width:100%;height:100%;display:flex;align-items:center;justify-content:center}.pause-ad .ad-content img{max-width:100%;max-height:100%}\n",""]);const r=a},4843:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".playlist-component{color:#fff;float:left;height:35px;margin-top:5px;display:flex;align-items:center;position:relative}.playlist-component i{color:#fff;display:inline-block;font-size:22px;display:block;margin-top:7px;cursor:pointer}.playlist-component i+i{margin-left:3px}@media (min-width: 768px){.playlist-component i:hover+.player-tooltip{display:block}}.playlist-component .player-tooltip.prev{left:-10px}.playlist-component .player-tooltip.list{left:5px}.playlist-component .player-tooltip.next{right:-12px}.playlist-content{position:absolute;right:0;width:0px;padding-bottom:48px;box-sizing:border-box;height:100%;transition:all .38s ease-in-out;overflow:hidden}.playlist-content .list{background-color:#000;background-color:rgba(0,0,0,0.3);height:100%;overflow:auto}.playlist-content .list .video-item{color:#fff;padding:0px 10px;line-height:35px;font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;cursor:pointer}.playlist-content .list .video-item.active{background-color:#000;color:#00ddff}@media (min-width: 768px){.playlist-content .list .video-item:hover{background-color:#000;color:#00ddff}}.player-tooltip{position:absolute;display:none;font-size:12px;color:#fff;line-height:28px;letter-spacing:0;text-align:center;background:#3c3c3c;box-shadow:0 0 5px 0 rgba(0,0,0,0.1);height:28px;top:-48px;padding:0 5px;white-space:nowrap}.playlist-skip-tip{padding:5px 15px;position:absolute;top:50%;left:50%;z-index:30;line-height:30px;font-size:14px;border-radius:4px;background:rgba(255,255,255,0.8);color:#000;text-align:center;transform:translate(-50%, -50%)}\n",""]);const r=a},9918:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".preview-hide{display:none !important}.preview-vod-component{display:none}.preview-vod-component .preview-component-layer{position:absolute;width:100%;height:100%;top:0;left:0;background-color:rgba(0,0,0,0.8);z-index:9998;box-sizing:border-box;padding:20px 20px 70px;display:none}.preview-vod-component .preview-component-layer .preview-close{position:absolute;right:18px;top:15px;border-radius:50%;color:#000;background:#fff;width:20px;height:20px;text-align:center;line-height:18px;vertical-align:top;color:#333;box-shadow:0 0 5px rgba(0,0,0,0.3);cursor:pointer;z-index:9998}.preview-vod-component .preview-component-layer .preview-custom{width:100%;height:100%;position:relative}.preview-vod-component .preview-component-layer .preview-custom .preview-default{font-size:14px;color:#fff;text-align:center;position:absolute;top:50%;transform:translateY(-50%);width:100%}.preview-vod-component .preview-custom{color:#fff;font-size:14px}.preview-vod-component .preview-component-tip{position:absolute;bottom:50px;left:25px;border-radius:15px;background-color:#333;color:#fff;line-height:26px;font-size:14px;padding:0 12px;z-index:9998}.preview-vod-component .preview-vod-close{font-size:18px;cursor:pointer;padding:0 5px;font-family:PingFangSC-Regular, sans-serif;font-weight:200}.preview-vod-component .preview-vod-close:hover{color:#00c1de}\n",""]);const r=a},2008:(t,A,e)=>{"use strict";e.d(A,{A:()=>v});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o),r=e(4417),l=e.n(r),s=new URL(e(1961),e.b),c=new URL(e(9099),e.b),d=new URL(e(3655),e.b),p=new URL(e(6999),e.b),m=new URL(e(2322),e.b),h=a()(i()),u=l()(s),y=l()(s,{hash:"#iefix"}),g=l()(c),M=l()(d),f=l()(p),x=l()(m,{hash:"#iconfont"});h.push([t.id,`@font-face{font-family:"iconfont";src:url(${u});src:url(${y}) format("embedded-opentype"),url(${g}) format("woff2"),url(${M}) format("woff"),url(${f}) format("truetype"),url(${x}) format("svg")}.iconfont{font-family:"iconfont" !important;font-size:16px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.icon-play:before{content:"\\e726"}.icon-arrowdown:before{content:"\\e623"}.clearfix:before,.clearfix:after{content:"";display:table;line-height:0}.clearfix:after{clear:both}.clearfix{*zoom:1}.progress-component{padding:8px;background-color:rgba(0,0,0,0.35);position:absolute;display:none;box-sizing:border-box;bottom:55px;font-size:12px;color:#fff;height:74px;z-index:10}.progress-component .progress-content{height:100%;position:relative;padding-right:45px}.progress-component .img-wrap{float:left;width:100px;text-align:center;border:1px solid rgba(0,0,0,0.6);height:100%}.progress-component .img-wrap img{vertical-align:top;max-width:100%;max-height:100%;object-fit:contain}.progress-component .info{float:left;padding-left:10px;width:150px}.progress-component .info .describe{line-height:18px;margin-top:8px;height:36px;overflow:hidden}.progress-component .pregress-play-btn{display:inline-block;position:absolute;right:6px;bottom:0px;font-size:28px;color:#ca7838;cursor:pointer}.progress-component .icon-arrowdown{left:-2px;position:absolute;font-size:31px;color:rgba(0,0,0,0.35);line-height:6px;top:100%}\n`,""]);const v=h},4200:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".player-hidden{display:none !important}.quality-components{float:right;color:#fff;height:35px;position:relative;box-sizing:border-box;margin-top:4px}.current-quality{display:flex;height:100%;align-items:center;justify-content:center;width:70px;cursor:pointer}.quality-list{position:absolute;bottom:46px;display:none;padding:0;margin:0;list-style:none;width:70px}.quality-list li{text-align:center;width:100%;line-height:30px;background-color:rgba(0,0,0,0.6);cursor:pointer}.quality-list li.current{color:#00c1de}.quality-list li+li{border-top:1px solid rgba(78,78,78,0.3)}.quality-list li:hover{background-color:rgba(0,0,0,0.5)}.quality-modal{position:absolute;bottom:20%;left:5%;background-color:rgba(0,0,0,0.6);border-radius:5px;color:#fff;padding:10px 15px;font-size:14px;display:none}.quality-modal span.current-quality-tag{color:#00c1de}@media (max-width: 768px){.current-quality{width:40px}.quality-list li{width:45px}}\n",""]);const r=a},6175:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".player-hidden{display:none !important}.rate-components{float:right;color:#fff;height:35px;position:relative;box-sizing:border-box;margin-top:5px}.current-rate{display:flex;height:100%;align-items:center;justify-content:center;width:70px;cursor:pointer}.rate-list{position:absolute;bottom:46px;display:none;padding:0;margin:0;list-style:none}.rate-list li{text-align:center;width:70px;line-height:30px;background-color:rgba(0,0,0,0.6);cursor:pointer}.rate-list li.current{color:#00c1de}.rate-list li+li{border-top:1px solid rgba(78,78,78,0.3)}.rate-list li:hover{background-color:rgba(0,0,0,0.5)}@media (max-width: 768px){.current-rate{width:40px}.rate-list li{width:43px}}\n",""]);const r=a},6481:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,'.aliplayer-rotate-mirror{float:right;color:#fff;display:flex;align-items:center;height:35px;position:relative;margin-top:5px}.aliplayer-rotate-mirror i{color:#fff;display:inline-block;font-size:22px;display:block;margin-top:7px;cursor:pointer;margin-right:10px}.aliplayer-rotate-mirror i.icon-player-switch{font-size:24px;font-weight:bold}@media (min-width: 768px){.aliplayer-rotate-mirror i:hover+.player-tooltip{display:block}}.aliplayer-rotate-mirror .mirror-option{position:absolute;background-color:#3c3c3c;top:0;transform:translateY(-103%);right:-20px}.aliplayer-rotate-mirror .mirror-option .mirror-item{font-size:14px;color:#ebecec;line-height:30px;white-space:nowrap;padding:0 15px;cursor:pointer;user-select:none}.aliplayer-rotate-mirror .mirror-option .mirror-item[data-id="counterclockwise"]{display:none}.aliplayer-rotate-mirror .mirror-option .mirror-item[data-id="clockwise"]{display:none}.aliplayer-rotate-mirror .mirror-option .mirror-item.active{background-color:rgba(216,216,216,0.1);color:#00c1de}.aliplayer-rotate-mirror .player-tooltip.counterclockwise{left:-35px}.aliplayer-rotate-mirror .player-tooltip.clockwise{left:-5px}.aliplayer-rotate-mirror .player-tooltip.switch{right:7px}@media (max-width: 768px){.aliplayer-rotate-mirror .mirror-option .mirror-item[data-id="counterclockwise"]{display:block}.aliplayer-rotate-mirror .mirror-option .mirror-item[data-id="clockwise"]{display:block}}\n',""]);const r=a},2484:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".start-ad{width:100%;height:100%;box-sizing:border-box;background-color:#000;position:absolute;z-index:9999}.start-ad .tip{position:absolute;right:5px;top:5px;background-color:#333;color:#fff;font-size:14px;text-align:center;padding:3px 7px}.start-ad .tip i{font-style:normal}.start-ad a{width:100%;height:100%;display:inline-block;text-align:center;display:flex;align-items:center;justify-content:center}.start-ad a img{max-width:100%;max-height:100%}\n",""]);const r=a},9218:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".player-hidden{display:none !important}.track-components{float:right;color:#fff;height:35px;position:relative;box-sizing:border-box;margin-top:5px}.current-track{display:flex;height:100%;align-items:center;justify-content:center;width:100px;cursor:pointer}.track-list{position:absolute;bottom:46px;display:none;padding:0;margin:0;list-style:none;height:auto;width:100px;overflow-y:scroll}.track-list li{text-align:center;width:100px;line-height:30px;background-color:rgba(0,0,0,0.6);cursor:pointer}.track-list li.current{color:#00c1de}.track-list li+li{border-top:1px solid rgba(78,78,78,0.3)}.track-list li:hover{background-color:rgba(0,0,0,0.5)}@media (max-width: 768px){.current-track{width:40px}.track-list li{width:43px}}.prism-player .prism-info-display .switchimg{color:#fff}\n",""]);const r=a},6613:(t,A,e)=>{"use strict";e.d(A,{A:()=>r});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o)()(i());a.push([t.id,".video-ad-component{width:100%;height:100%;position:absolute;top:0;left:0;z-index:2147483647;background-color:#000;font-size:12px}.video-ad-component #video-ad-content{width:100%;height:100%}.video-ad-component .video-ad-link{width:100%;height:100%;display:block;position:absolute;top:0;left:0}.video-ad-component .video-ad-close{background-color:rgba(59,59,59,0.85);position:absolute;top:15px;right:20px;color:#fff;line-height:26px;padding:0 10px;user-select:none}.video-ad-component .video-ad-close #video-ad-duration{color:#00c1de;vertical-align:top;font-size:14px;position:relative;display:inline-block;text-align:right}.video-ad-component .video-ad-close #video-ad-duration::after{background-color:#fff;position:absolute;content:'';right:-7px;top:8px;bottom:8px;width:1px}.video-ad-component .video-ad-close label{cursor:pointer;display:inline-block}.video-ad-component .video-ad-close .icon-close{font-size:12px;vertical-align:top}@media (min-width: 768px){.video-ad-component .video-ad-close .icon-close:hover{color:#00c1de}}.video-ad-component .video-ad-close .video-ad-close-text{padding:0 5px 0 10px}.video-ad-component .video-ad-detail{position:absolute;right:35px;bottom:30px;background-color:rgba(0,222,255,0.85);color:#fff;padding:8px 12px;user-select:none;cursor:pointer;transition:background-color .15s}@media (min-width: 768px){.video-ad-component .video-ad-detail:hover{background-color:#00deff}}.video-ad-component .autoplay-video-ad{position:absolute;color:#fff;top:50%;left:50%;text-align:center;padding:10px;border-radius:5px;background-color:rgba(144,147,153,0.85);transform:translate(-50%, -50%);display:none}.video-ad-component .autoplay-video-ad i{font-size:42px;margin-bottom:7px;display:inline-block;cursor:pointer}.video-ad-component .autoplay-video-ad i+span{margin-bottom:5px}.video-ad-component .autoplay-video-ad span{display:block}.controlbar-element-hidden{display:none !important}\n",""]);const r=a},5570:(t,A,e)=>{"use strict";e.d(A,{A:()=>p});var n=e(1601),i=e.n(n),o=e(6314),a=e.n(o),r=e(4417),l=e.n(r),s=new URL(e(9379),e.b),c=a()(i()),d=l()(s);c.push([t.id,`.player-olympic-player-next{width:32px;height:32px;background:url(${d}) center no-repeat;background-size:contain;float:left;margin-left:10px;margin-top:8px;cursor:pointer;position:relative}.player-olympic-player-next-tip{position:absolute;top:-45px;display:none;font-size:12px;color:#ffffff;line-height:28px;letter-spacing:0;text-align:center;background:#3c3c3c;box-shadow:0 0 5px 0 rgba(0,0,0,0.1);width:58px;height:28px}.player-olympic-player-next:hover .player-olympic-player-next-tip{display:block}\n`,""]);const p=c},6314:t=>{"use strict";t.exports=function(t){var A=[];return A.toString=function(){return this.map((function(A){var e="",n=void 0!==A[5];return A[4]&&(e+="@supports (".concat(A[4],") {")),A[2]&&(e+="@media ".concat(A[2]," {")),n&&(e+="@layer".concat(A[5].length>0?" ".concat(A[5]):""," {")),e+=t(A),n&&(e+="}"),A[2]&&(e+="}"),A[4]&&(e+="}"),e})).join("")},A.i=function(t,e,n,i,o){"string"==typeof t&&(t=[[null,t,void 0]]);var a={};if(n)for(var r=0;r<this.length;r++){var l=this[r][0];null!=l&&(a[l]=!0)}for(var s=0;s<t.length;s++){var c=[].concat(t[s]);n&&a[c[0]]||(void 0!==o&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=o),e&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=e):c[2]=e),i&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=i):c[4]="".concat(i)),A.push(c))}},A}},4417:t=>{"use strict";t.exports=function(t,A){return A||(A={}),t?(t=String(t.__esModule?t.default:t),/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),A.hash&&(t+=A.hash),/["'() \t\n]|(%20)/.test(t)||A.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t):t}},1601:t=>{"use strict";t.exports=function(t){return t[1]}},176:(t,A,e)=>{"use strict";e.d(A,{A:()=>x});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i=window.device,o={},a=[];window.device=o;var r=window.document.documentElement,l=window.navigator.userAgent.toLowerCase(),s=["googletv","viera","smarttv","internet.tv","netcast","nettv","appletv","boxee","kylo","roku","dlnadoc","pov_tv","hbbtv","ce-html"];function c(t,A){return-1!==t.indexOf(A)}function d(t){return c(l,t)}function p(t){return r.className.match(new RegExp(t,"i"))}function m(t){var A=null;p(t)||(A=r.className.replace(/^\s+|\s+$/g,""),r.className=A+" "+t)}function h(t){p(t)&&(r.className=r.className.replace(" "+t,""))}function u(){o.landscape()?(h("portrait"),m("landscape"),y("landscape")):(h("landscape"),m("portrait"),y("portrait")),f()}function y(t){for(var A=0;A<a.length;A++)a[A](t)}o.macos=function(){return d("mac")},o.ios=function(){return o.iphone()||o.ipod()||o.ipad()},o.iphone=function(){return!o.windows()&&d("iphone")},o.ipod=function(){return d("ipod")},o.ipad=function(){var t="MacIntel"===navigator.platform&&navigator.maxTouchPoints>1;return d("ipad")||t},o.android=function(){return!o.windows()&&d("android")},o.androidPhone=function(){return o.android()&&d("mobile")},o.androidTablet=function(){return o.android()&&!d("mobile")},o.blackberry=function(){return d("blackberry")||d("bb10")},o.blackberryPhone=function(){return o.blackberry()&&!d("tablet")},o.blackberryTablet=function(){return o.blackberry()&&d("tablet")},o.windows=function(){return d("windows")},o.windowsPhone=function(){return o.windows()&&d("phone")},o.windowsTablet=function(){return o.windows()&&d("touch")&&!o.windowsPhone()},o.fxos=function(){return(d("(mobile")||d("(tablet"))&&d(" rv:")},o.fxosPhone=function(){return o.fxos()&&d("mobile")},o.fxosTablet=function(){return o.fxos()&&d("tablet")},o.meego=function(){return d("meego")},o.cordova=function(){return window.cordova&&"file:"===location.protocol},o.nodeWebkit=function(){return"object"===n(window.process)},o.mobile=function(){return o.androidPhone()||o.iphone()||o.ipod()||o.windowsPhone()||o.blackberryPhone()||o.fxosPhone()||o.meego()},o.tablet=function(){return o.ipad()||o.androidTablet()||o.blackberryTablet()||o.windowsTablet()||o.fxosTablet()},o.desktop=function(){return!o.tablet()&&!o.mobile()},o.television=function(){for(var t=0;t<s.length;){if(d(s[t]))return!0;t++}return!1},o.portrait=function(){return screen.orientation&&Object.prototype.hasOwnProperty.call(window,"onorientationchange")?c(screen.orientation.type,"portrait"):o.ios()&&Object.prototype.hasOwnProperty.call(window,"orientation")?90!==Math.abs(window.orientation):window.innerHeight/window.innerWidth>1},o.landscape=function(){return screen.orientation&&Object.prototype.hasOwnProperty.call(window,"onorientationchange")?c(screen.orientation.type,"landscape"):o.ios()&&Object.prototype.hasOwnProperty.call(window,"orientation")?90===Math.abs(window.orientation):window.innerHeight/window.innerWidth<1},o.noConflict=function(){return window.device=i,this},o.ios()?o.ipad()?m("ios ipad tablet"):o.iphone()?m("ios iphone mobile"):o.ipod()&&m("ios ipod mobile"):o.macos()?m("macos desktop"):o.android()?o.androidTablet()?m("android tablet"):m("android mobile"):o.blackberry()?o.blackberryTablet()?m("blackberry tablet"):m("blackberry mobile"):o.windows()?o.windowsTablet()?m("windows tablet"):o.windowsPhone()?m("windows mobile"):m("windows desktop"):o.fxos()?o.fxosTablet()?m("fxos tablet"):m("fxos mobile"):o.meego()?m("meego mobile"):o.nodeWebkit()?m("node-webkit"):o.television()?m("television"):o.desktop()&&m("desktop"),o.cordova()&&m("cordova"),o.onChangeOrientation=function(t){"function"==typeof t&&a.push(t)};var g="resize";function M(t){for(var A=0;A<t.length;A++)if(o[t[A]]())return t[A];return"unknown"}function f(){o.orientation=M(["portrait","landscape"])}Object.prototype.hasOwnProperty.call(window,"onorientationchange")&&(g="orientationchange"),window.addEventListener?window.addEventListener(g,u,!1):window.attachEvent?window.attachEvent(g,u):window[g]=u,u(),o.type=M(["mobile","tablet","desktop"]),o.os=M(["ios","iphone","ipad","ipod","android","blackberry","macos","windows","fxos","meego","television"]),f();const x=o},5072:t=>{"use strict";var A=[];function e(t){for(var e=-1,n=0;n<A.length;n++)if(A[n].identifier===t){e=n;break}return e}function n(t,n){for(var o={},a=[],r=0;r<t.length;r++){var l=t[r],s=n.base?l[0]+n.base:l[0],c=o[s]||0,d="".concat(s," ").concat(c);o[s]=c+1;var p=e(d),m={css:l[1],media:l[2],sourceMap:l[3],supports:l[4],layer:l[5]};if(-1!==p)A[p].references++,A[p].updater(m);else{var h=i(m,n);n.byIndex=r,A.splice(r,0,{identifier:d,updater:h,references:1})}a.push(d)}return a}function i(t,A){var e=A.domAPI(A);return e.update(t),function(A){if(A){if(A.css===t.css&&A.media===t.media&&A.sourceMap===t.sourceMap&&A.supports===t.supports&&A.layer===t.layer)return;e.update(t=A)}else e.remove()}}t.exports=function(t,i){var o=n(t=t||[],i=i||{});return function(t){t=t||[];for(var a=0;a<o.length;a++){var r=e(o[a]);A[r].references--}for(var l=n(t,i),s=0;s<o.length;s++){var c=e(o[s]);0===A[c].references&&(A[c].updater(),A.splice(c,1))}o=l}}},7659:t=>{"use strict";var A={};t.exports=function(t,e){var n=function(t){if(void 0===A[t]){var e=document.querySelector(t);if(window.HTMLIFrameElement&&e instanceof window.HTMLIFrameElement)try{e=e.contentDocument.head}catch(t){e=null}A[t]=e}return A[t]}(t);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(e)}},540:t=>{"use strict";t.exports=function(t){var A=document.createElement("style");return t.setAttributes(A,t.attributes),t.insert(A,t.options),A}},5056:(t,A,e)=>{"use strict";t.exports=function(t){var A=e.nc;A&&t.setAttribute("nonce",A)}},7825:t=>{"use strict";t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var A=t.insertStyleElement(t);return{update:function(e){!function(t,A,e){var n="";e.supports&&(n+="@supports (".concat(e.supports,") {")),e.media&&(n+="@media ".concat(e.media," {"));var i=void 0!==e.layer;i&&(n+="@layer".concat(e.layer.length>0?" ".concat(e.layer):""," {")),n+=e.css,i&&(n+="}"),e.media&&(n+="}"),e.supports&&(n+="}");var o=e.sourceMap;o&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),A.styleTagTransform(n,t,A.options)}(A,t,e)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(A)}}}},1113:t=>{"use strict";t.exports=function(t,A){if(A.styleSheet)A.styleSheet.cssText=t;else{for(;A.firstChild;)A.removeChild(A.firstChild);A.appendChild(document.createTextNode(t))}}},7214:t=>{t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiID4KPCEtLQoyMDEzLTktMzA6IENyZWF0ZWQuCi0tPgo8c3ZnPgo8bWV0YWRhdGE+CkNyZWF0ZWQgYnkgaWNvbmZvbnQKPC9tZXRhZGF0YT4KPGRlZnM+Cgo8Zm9udCBpZD0iaWNvbmZvbnQiIGhvcml6LWFkdi14PSIxMDI0IiA+CiAgPGZvbnQtZmFjZQogICAgZm9udC1mYW1pbHk9Imljb25mb250IgogICAgZm9udC13ZWlnaHQ9IjUwMCIKICAgIGZvbnQtc3RyZXRjaD0ibm9ybWFsIgogICAgdW5pdHMtcGVyLWVtPSIxMDI0IgogICAgYXNjZW50PSI4OTYiCiAgICBkZXNjZW50PSItMTI4IgogIC8+CiAgICA8bWlzc2luZy1nbHlwaCAvPgogICAgCiAgICA8Z2x5cGggZ2x5cGgtbmFtZT0ieCIgdW5pY29kZT0ieCIgaG9yaXotYWR2LXg9IjEwMDEiCmQ9Ik0yODEgNTQzcS0yNyAtMSAtNTMgLTFoLTgzcS0xOCAwIC0zNi41IC02dC0zMi41IC0xOC41dC0yMyAtMzJ0LTkgLTQ1LjV2LTc2aDkxMnY0MXEwIDE2IC0wLjUgMzB0LTAuNSAxOHEwIDEzIC01IDI5dC0xNyAyOS41dC0zMS41IDIyLjV0LTQ5LjUgOWgtMTMzdi05N2gtNDM4djk3ek05NTUgMzEwdi01MnEwIC0yMyAwLjUgLTUydDAuNSAtNTh0LTEwLjUgLTQ3LjV0LTI2IC0zMHQtMzMgLTE2dC0zMS41IC00LjVxLTE0IC0xIC0yOS41IC0wLjUKdC0yOS41IDAuNWgtMzJsLTQ1IDEyOGgtNDM5bC00NCAtMTI4aC0yOWgtMzRxLTIwIDAgLTQ1IDFxLTI1IDAgLTQxIDkuNXQtMjUuNSAyM3QtMTMuNSAyOS41dC00IDMwdjE2N2g5MTF6TTE2MyAyNDdxLTEyIDAgLTIxIC04LjV0LTkgLTIxLjV0OSAtMjEuNXQyMSAtOC41cTEzIDAgMjIgOC41dDkgMjEuNXQtOSAyMS41dC0yMiA4LjV6TTMxNiAxMjNxLTggLTI2IC0xNCAtNDhxLTUgLTE5IC0xMC41IC0zN3QtNy41IC0yNXQtMyAtMTV0MSAtMTQuNQp0OS41IC0xMC41dDIxLjUgLTRoMzdoNjdoODFoODBoNjRoMzZxMjMgMCAzNCAxMnQyIDM4cS01IDEzIC05LjUgMzAuNXQtOS41IDM0LjVxLTUgMTkgLTExIDM5aC0zNjh6TTMzNiA0OTh2MjI4cTAgMTEgMi41IDIzdDEwIDIxLjV0MjAuNSAxNS41dDM0IDZoMTg4cTMxIDAgNTEuNSAtMTQuNXQyMC41IC01Mi41di0yMjdoLTMyN3oiIC8+CiAgICAKCiAgICAKICAgIDxnbHlwaCBnbHlwaC1uYW1lPSIxbXVsdSIgdW5pY29kZT0iJiM1ODk0NzsiIGQ9Ik0xMDcuMiA2ODMuMm0tNjcuMiAwYTQuMiA0LjIgMCAxIDAgMTM0LjQgMCA0LjIgNC4yIDAgMSAwLTEzNC40IDBaTTk4MC44IDc1MC40IDI5Ny42IDc1MC40Yy05LjYgMC0xNi04LTE2LTE2bDAtMTAyLjRjMC05LjYgOC0xNiAxNi0xNmw2ODMuMiAwYzkuNiAwIDE2IDggMTYgMTZsMCAxMDIuNEM5OTYuOCA3NDQgOTg4LjggNzUwLjQgOTgwLjggNzUwLjR6TTk2IDM5OC40bS02Ny4yIDBhNC4yIDQuMiAwIDEgMCAxMzQuNCAwIDQuMiA0LjIgMCAxIDAtMTM0LjQgMFpNOTY4IDQ2NS42IDI4NC44IDQ2NS42Yy05LjYgMC0xNi04LTE2LTE2bDAtMTAyLjRjMC05LjYgOC0xNiAxNi0xNmw2ODMuMiAwYzkuNiAwIDE2IDggMTYgMTZsMCAxMDIuNEM5ODQgNDU3LjYgOTc3LjYgNDY1LjYgOTY4IDQ2NS42ek05NiAxMDAuOG0tNjcuMiAwYTQuMiA0LjIgMCAxIDAgMTM0LjQgMCA0LjIgNC4yIDAgMSAwLTEzNC40IDBaTTk2OCAxNjggMjg0LjggMTY4Yy05LjYgMC0xNi04LTE2LTE2bDAtMTAyLjRjMC05LjYgOC0xNiAxNi0xNmw2ODMuMiAwYzkuNiAwIDE2IDggMTYgMTZsMCAxMDIuNEM5ODQgMTYwIDk3Ny42IDE2OCA5NjggMTY4eiIgIGhvcml6LWFkdi14PSIxMDI0IiAvPgoKICAgIAogICAgPGdseXBoIGdseXBoLW5hbWU9InNraXBuZXh0IiB1bmljb2RlPSImIzYwMDYyOyIgZD0iTTY4Mi42NjY2NjcgMTIyLjAyNjY2NyA3NjggMTIyLjAyNjY2NyA3NjggNjM0LjAyNjY2NyA2ODIuNjY2NjY3IDYzNC4wMjY2NjdNMjU2IDEyMi4wMjY2NjcgNjE4LjY2NjY2NyAzNzguMDI2NjY3IDI1NiA2MzQuMDI2NjY3IDI1NiAxMjIuMDI2NjY3WiIgIGhvcml6LWFkdi14PSIxMDI0IiAvPgoKICAgIAogICAgPGdseXBoIGdseXBoLW5hbWU9ImNsb3NlIiB1bmljb2RlPSImIzU4OTAyOyIgZD0iTTg3Ny4yMTYgNDA0LjE5Mk01NzUuMzI4IDM4NS41MDQgOTQ2Ljc4NCA3NTUuMzI4YzE3LjU2OCAxNy41MDQgMTcuNjY0IDQ1LjgyNCAwLjE5MiA2My40MjQtMTcuNTA0IDE3LjYzMi00NS43OTIgMTcuNjY0LTYzLjM2IDAuMTkyTDUxMi4wMzIgNDQ5LjA1NiAxNDMuNzEyIDgxOC43ODRDMTI2LjMwNCA4MzYuMjg4IDk3LjkyIDgzNi4zNTIgODAuMzg0IDgxOC44OCA2Mi44NDggODAxLjM3NiA2Mi44MTYgNzcyLjk5MiA4MC4yODggNzU1LjQyNGwzNjguMjI0LTM2OS42MzJMNzcuMjE2IDE2LjE5MmMtMTcuNTY4LTE3LjUwNC0xNy42NjQtNDUuODI0LTAuMTkyLTYzLjQyNCA4LjczNi04LjggMjAuMjU2LTEzLjIxNiAzMS43NzYtMTMuMjE2IDExLjQyNCAwIDIyLjg0OCA0LjM1MiAzMS41ODQgMTMuMDU2bDM3MS4zNiAzNjkuNjk2IDM3MS42OC0zNzMuMDg4Qzg5Mi4xOTItNTkuNjE2IDkwMy42OC02NCA5MTUuMTY4LTY0YzExLjQ1NiAwIDIyLjkxMiA0LjM4NCAzMS42NDggMTMuMDg4IDE3LjUwNCAxNy41MDQgMTcuNTY4IDQ1LjgyNCAwLjA5NiA2My4zOTJMNTc1LjMyOCAzODUuNTA0IDU3NS4zMjggMzg1LjUwNHpNNTc1LjMyOCAzODUuNTA0IiAgaG9yaXotYWR2LXg9IjEwMjQiIC8+CgogICAgCiAgICA8Z2x5cGggZ2x5cGgtbmFtZT0icGxheSIgdW5pY29kZT0iJiM1ODk0NTsiIGQ9Ik01MTIgODkzLjk4NHEtMTM5LjAwOCAwLTI1Ni41MTItNjguNTEyVDY5LjQ3MiA2MzkuNDU2IDAuOTYgMzgyLjk0NHQ2OC41MTItMjU2LjUxMiAxODYuMDE2LTE4Ni4wMTZUNTEyLTEyOC4wOTZ0MjU2LjUxMiA2OC41MTIgMTg2LjAxNiAxODYuMDE2IDY4LjUxMiAyNTYuNTEyLTY4LjUxMiAyNTYuNTEyLTE4Ni4wMTYgMTg2LjAxNlQ1MTIgODkzLjk4NHogbTIwMC01MjQuOTkybC0yOTEuMDA4LTI0Ny4wMDhxLTYuMDE2LTYuMDE2LTE1LjAwOC02LjAxNi04IDAtMTQuNDk2IDUuNTA0dC02LjQ5NiAxNC40OTZWNjMwLjk3NnEwIDggNi40OTYgMTMuNTA0dDE1LjAwOCA1LjUwNCAxNC40OTYtNi4wMTZsMjkxLjAwOC0yNDcuMDA4cTYuMDE2LTYuMDE2IDYuMDE2LTE0LjAxNnQtNi4wMTYtMTQuMDE2eiIgIGhvcml6LWFkdi14PSIxMDI0IiAvPgoKICAgIAogICAgPGdseXBoIGdseXBoLW5hbWU9InNraXAtcHJldmlvdXMiIHVuaWNvZGU9IiYjNTkwOTE7IiBkPSJNMjU2IDY0MGg4NS4zMzMzMzN2LTUxMkgyNTZ6TTQwNS4zMzMzMzMgMzg0bDM2Mi42NjY2NjctMjU2VjY0MHoiICBob3Jpei1hZHYteD0iMTAyNCIgLz4KCiAgICAKICAgIDxnbHlwaCBnbHlwaC1uYW1lPSJhcnJvdy1yb3RhdGUtbGVmdCIgdW5pY29kZT0iJiM1ODk2ODsiIGQ9Ik05Ni40IDc3OS4xbDgwLjYtODAuNmM4MC43IDgxLjMgMTkyLjUgMTMxLjggMzE2LjEgMTMyIDI0Ni4yIDAuNSA0NDcuMy0xOTkuNyA0NDcuNy00NDUuOUM5NDEuMiAxMzcuNSA3NDEtNjMgNDk0LTYzYy0xMzEuOSAwLTI1MC40IDU3LjEtMzMyLjIgMTQ4LTM3LjIgNDEuNC03LjkgMTA3LjQgNDcuOCAxMDcuOSAxOC41IDAuMiAzNi4yLTcuNSA0OC41LTIxLjIgNTguMS02NC41IDE0Mi4zLTEwNS4xIDIzNS45LTEwNS4xIDE3Ni4xIDAgMzE4LjggMTQzLjUgMzE3LjMgMzIwLTEuNSAxNzYuMi0xNDUuMSAzMTYuNi0zMjEuNCAzMTQuNC04Ni42LTEuMS0xNjQuOC0zNi45LTIyMS40LTk0LjFsNzkuNi03OS42YzExLjgtMTEuOCAzLjQtMzEuOS0xMy4yLTMxLjlIODMuMmMtMTAuMyAwLTE4LjcgOC4zLTE4LjcgMTguN1Y3NjUuOWMwIDE2LjYgMjAuMSAyNSAzMS45IDEzLjJ6IiAgaG9yaXotYWR2LXg9IjEwMjQiIC8+CgogICAgCiAgICA8Z2x5cGggZ2x5cGgtbmFtZT0iYXJyb3ctcm90YXRlLXJpZ2h0IiB1bmljb2RlPSImIzU4OTY5OyIgZD0iTTk0MC44IDc2NS45di0yNTEuOGMwLTEwLjQtOC40LTE4LjctMTguNy0xOC43SDY3MC40Yy0xNi42IDAtMjUgMjAuMS0xMy4yIDMxLjlsNzkuNiA3OS42Yy01Ni42IDU3LjItMTM0LjggOTMtMjIxLjQgOTQuMS0xNzYuMyAyLjItMzE5LjktMTM4LjItMzIxLjQtMzE0LjQtMS41LTE3Ni41IDE0MS4yLTMyMCAzMTcuMy0zMjAgOTMuNiAwIDE3Ny44IDQwLjYgMjM1LjkgMTA1LjEgMTIuMyAxMy43IDMwIDIxLjQgNDguNSAyMS4yIDU1LjctMC41IDg1LTY2LjUgNDcuOC0xMDcuOS04MS44LTkwLjktMjAwLjMtMTQ4LTMzMi4yLTE0OC0yNDcgMC00NDcuMiAyMDAuNS00NDYuOCA0NDcuNkM2NC45IDYzMC44IDI2NiA4MzEgNTEyLjIgODMwLjVjMTIzLjYtMC4yIDIzNS40LTUwLjcgMzE2LjEtMTMybDgwLjYgODAuNmMxMS44IDExLjggMzEuOSAzLjQgMzEuOS0xMy4yeiIgIGhvcml6LWFkdi14PSIxMDI0IiAvPgoKICAgIAogICAgPGdseXBoIGdseXBoLW5hbWU9ImJvZmFuZ3FpLWRhbm11Z3VhbiIgdW5pY29kZT0iJiM1OTAzMDsiIGQ9Ik01NTguNTQ1NDU1IDE3NC41NDU0NTVIMTU1LjkyNzI3M0MxMjMuMzQ1NDU1IDE3NC41NDU0NTUgOTMuMDkwOTA5IDIwNC44IDkzLjA5MDkwOSAyMzcuMzgxODE4VjU3Ny4xNjM2MzZDOTMuMDkwOTA5IDYwOS43NDU0NTUgMTIzLjM0NTQ1NSA2NDAgMTU1LjkyNzI3MyA2NDBoNTcyLjUwOTA5MWMzMi41ODE4MTggMCA2Mi44MzYzNjQtMzAuMjU0NTQ1IDYyLjgzNjM2My02Mi44MzYzNjRWNTQ2LjkwOTA5MWMwLTEzLjk2MzYzNiA5LjMwOTA5MS0yMy4yNzI3MjcgMjMuMjcyNzI4LTIzLjI3MjcyN3MyMy4yNzI3MjcgOS4zMDkwOTEgMjMuMjcyNzI3IDIzLjI3MjcyN3YzMC4yNTQ1NDVjMCA1OC4xODE4MTgtNTEuMiAxMDkuMzgxODE4LTEwOS4zODE4MTggMTA5LjM4MTgxOUgxNTUuOTI3MjczQzk3Ljc0NTQ1NSA2ODYuNTQ1NDU1IDQ2LjU0NTQ1NSA2MzUuMzQ1NDU1IDQ2LjU0NTQ1NSA1NzcuMTYzNjM2di0zMzkuNzgxODE4QzQ2LjU0NTQ1NSAxNzkuMiA5Ny43NDU0NTUgMTI4IDE1NS45MjcyNzMgMTI4SDU1OC41NDU0NTVjMTMuOTYzNjM2IDAgMjMuMjcyNzI3IDkuMzA5MDkxIDIzLjI3MjcyNyAyMy4yNzI3MjdzLTkuMzA5MDkxIDIzLjI3MjcyNy0yMy4yNzI3MjcgMjMuMjcyNzI4ek0zMjUuODE4MTgyIDU0Ni45MDkwOTFoNDYuNTQ1NDU0di00Ni41NDU0NTVoLTQ2LjU0NTQ1NHpNNDE4LjkwOTA5MSA1NDYuOTA5MDkxaDE4Ni4xODE4MTh2LTQ2LjU0NTQ1NWgtMTg2LjE4MTgxOHpNMTg2LjE4MTgxOCA0MzAuNTQ1NDU1aDQ2LjU0NTQ1NXYtNDYuNTQ1NDU1SDE4Ni4xODE4MTh6TTI3OS4yNzI3MjcgNDMwLjU0NTQ1NWgxODYuMTgxODE4di00Ni41NDU0NTVoLTE4Ni4xODE4MTh6TTI3OS4yNzI3MjcgMzE0LjE4MTgxOGg0Ni41NDU0NTV2LTQ2LjU0NTQ1NGgtNDYuNTQ1NDU1ek0zNzIuMzYzNjM2IDMxNC4xODE4MThoMTg2LjE4MTgxOXYtNDYuNTQ1NDU0aC0xODYuMTgxODE5ek04MDIuOTA5MDkxIDQzMC41NDU0NTVjLTk1LjQxODE4MiAwLTE3NC41NDU0NTUtNzkuMTI3MjczLTE3NC41NDU0NTUtMTc0LjU0NTQ1NXM3OS4xMjcyNzMtMTc0LjU0NTQ1NSAxNzQuNTQ1NDU1LTE3NC41NDU0NTUgMTc0LjU0NTQ1NSA3OS4xMjcyNzMgMTc0LjU0NTQ1NCAxNzQuNTQ1NDU1LTc5LjEyNzI3MyAxNzQuNTQ1NDU1LTE3NC41NDU0NTQgMTc0LjU0NTQ1NXogbTAtMzAyLjU0NTQ1NWMtNjkuODE4MTgyIDAtMTI4IDU4LjE4MTgxOC0xMjggMTI4IDAgMjMuMjcyNzI3IDYuOTgxODE4IDQ0LjIxODE4MiAxNi4yOTA5MDkgNjIuODM2MzY0bDE3NC41NDU0NTUtMTc0LjU0NTQ1NWMtMTguNjE4MTgyLTkuMzA5MDkxLTM5LjU2MzYzNi0xNi4yOTA5MDktNjIuODM2MzY0LTE2LjI5MDkwOXogbTk3Ljc0NTQ1NCA0Ni41NDU0NTVMNzIxLjQ1NDU0NSAzNTMuNzQ1NDU1YzIzLjI3MjcyNyAxOC42MTgxODIgNTEuMiAzMC4yNTQ1NDUgODEuNDU0NTQ2IDMwLjI1NDU0NSA2OS44MTgxODIgMCAxMjgtNTguMTgxODE4IDEyOC0xMjggMC0zMC4yNTQ1NDUtMTEuNjM2MzY0LTYwLjUwOTA5MS0zMC4yNTQ1NDYtODEuNDU0NTQ1eiIgIGhvcml6LWFkdi14PSIxMDI0IiAvPgoKICAgIAogICAgPGdseXBoIGdseXBoLW5hbWU9ImJvZmFuZ3FpLWRhbm11a2FpIiB1bmljb2RlPSImIzU5MDMxOyIgZD0iTTU1OC41NDU0NTUgMTc0LjU0NTQ1NUgxNTUuOTI3MjczQzEyMy4zNDU0NTUgMTc0LjU0NTQ1NSA5My4wOTA5MDkgMjA0LjggOTMuMDkwOTA5IDIzNy4zODE4MThWNTc3LjE2MzYzNkM5My4wOTA5MDkgNjA5Ljc0NTQ1NSAxMjMuMzQ1NDU1IDY0MCAxNTUuOTI3MjczIDY0MGg1NzIuNTA5MDkxYzMyLjU4MTgxOCAwIDYyLjgzNjM2NC0zMC4yNTQ1NDUgNjIuODM2MzYzLTYyLjgzNjM2NFY1NDYuOTA5MDkxYzAtMTMuOTYzNjM2IDkuMzA5MDkxLTIzLjI3MjcyNyAyMy4yNzI3MjgtMjMuMjcyNzI3czIzLjI3MjcyNyA5LjMwOTA5MSAyMy4yNzI3MjcgMjMuMjcyNzI3djMwLjI1NDU0NWMwIDU4LjE4MTgxOC01MS4yIDEwOS4zODE4MTgtMTA5LjM4MTgxOCAxMDkuMzgxODE5SDE1NS45MjcyNzNDOTcuNzQ1NDU1IDY4Ni41NDU0NTUgNDYuNTQ1NDU1IDYzNS4zNDU0NTUgNDYuNTQ1NDU1IDU3Ny4xNjM2MzZ2LTMzOS43ODE4MThDNDYuNTQ1NDU1IDE3OS4yIDk3Ljc0NTQ1NSAxMjggMTU1LjkyNzI3MyAxMjhINTU4LjU0NTQ1NWMxMy45NjM2MzYgMCAyMy4yNzI3MjcgOS4zMDkwOTEgMjMuMjcyNzI3IDIzLjI3MjcyN3MtOS4zMDkwOTEgMjMuMjcyNzI3LTIzLjI3MjcyNyAyMy4yNzI3Mjh6TTMyNS44MTgxODIgNTQ2LjkwOTA5MWg0Ni41NDU0NTR2LTQ2LjU0NTQ1NWgtNDYuNTQ1NDU0ek00MTguOTA5MDkxIDU0Ni45MDkwOTFoMTg2LjE4MTgxOHYtNDYuNTQ1NDU1aC0xODYuMTgxODE4ek0xODYuMTgxODE4IDQzMC41NDU0NTVoNDYuNTQ1NDU1di00Ni41NDU0NTVIMTg2LjE4MTgxOHpNMjc5LjI3MjcyNyA0MzAuNTQ1NDU1aDE4Ni4xODE4MTh2LTQ2LjU0NTQ1NWgtMTg2LjE4MTgxOHpNMzcyLjM2MzYzNiAzMTQuMTgxODE4aDE4Ni4xODE4MTl2LTQ2LjU0NTQ1NGgtMTg2LjE4MTgxOXpNMjc5LjI3MjcyNyAzMTQuMTgxODE4aDQ2LjU0NTQ1NXYtNDYuNTQ1NDU0aC00Ni41NDU0NTV6TTgwMi45MDkwOTEgNDMwLjU0NTQ1NWMtOTUuNDE4MTgyIDAtMTc0LjU0NTQ1NS03OS4xMjcyNzMtMTc0LjU0NTQ1NS0xNzQuNTQ1NDU1czc5LjEyNzI3My0xNzQuNTQ1NDU1IDE3NC41NDU0NTUtMTc0LjU0NTQ1NSAxNzQuNTQ1NDU1IDc5LjEyNzI3MyAxNzQuNTQ1NDU0IDE3NC41NDU0NTUtNzkuMTI3MjczIDE3NC41NDU0NTUtMTc0LjU0NTQ1NCAxNzQuNTQ1NDU1eiBtMC0zMDIuNTQ1NDU1Yy02OS44MTgxODIgMC0xMjggNTguMTgxODE4LTEyOCAxMjhzNTguMTgxODE4IDEyOCAxMjggMTI4IDEyOC01OC4xODE4MTggMTI4LTEyOC01OC4xODE4MTgtMTI4LTEyOC0xMjh6TTkwMC42NTQ1NDUgMzA5LjUyNzI3M2MtOS4zMDkwOTEgOS4zMDkwOTEtMjMuMjcyNzI3IDkuMzA5MDkxLTMyLjU4MTgxOCAwbC04MS40NTQ1NDUtODEuNDU0NTQ2LTQ4Ljg3MjcyNyA0OC44NzI3MjhjLTkuMzA5MDkxIDkuMzA5MDkxLTIzLjI3MjcyNyA5LjMwOTA5MS0zMi41ODE4MTkgMC05LjMwOTA5MS05LjMwOTA5MS05LjMwOTA5MS0yMy4yNzI3MjcgMC0zMi41ODE4MTlsNjUuMTYzNjM3LTY1LjE2MzYzNmM5LjMwOTA5MS05LjMwOTA5MSAyMy4yNzI3MjctOS4zMDkwOTEgMzIuNTgxODE4IDBsOTcuNzQ1NDU0IDk3Ljc0NTQ1NWM5LjMwOTA5MSA5LjMwOTA5MSA5LjMwOTA5MSAyMy4yNzI3MjcgMCAzMi41ODE4MTh6IiAgaG9yaXotYWR2LXg9IjEwMjQiIC8+CgogICAgCiAgICA8Z2x5cGggZ2x5cGgtbmFtZT0ibmVpcm9uZ3FpZWh1YW4iIHVuaWNvZGU9IiYjNTkyNjU7IiBkPSJNMTk4LjQgMTkyaDUzNy42YzU0LjQgMCA5NiA0MS42IDk2IDk2djk2YzAgMTkuMiAxMi44IDMyIDMyIDMyczMyLTEyLjggMzItMzJ2LTk2YzAtODkuNi03MC40LTE2MC0xNjAtMTYwaC01NjMuMmwxMTguNC0xMTguNGMxMi44LTEyLjggMTIuOC0zMiAwLTQ0LjgtMTIuOC0xMi44LTMyLTEyLjgtNDQuOCAwbC0xNTYuOCAxNTYuOGMtNi40IDYuNC05LjYgMTYtOS42IDIyLjQgMCA5LjYgMy4yIDE2IDkuNiAyMi40bDE1Ni44IDE1Ni44YzEyLjggMTIuOCAzMiAxMi44IDQ0LjggMCAxMi44LTEyLjggMTIuOC0zMiAwLTQ0LjhsLTkyLjgtODYuNHogbTY0My4yIDQxNmgtNjE3LjZjLTU0LjQgMC05Ni00MS42LTk2LTk2di05NmMwLTE5LjItMTIuOC0zMi0zMi0zMnMtMzIgMTIuOC0zMiAzMnY5NmMwIDg5LjYgNzAuNCAxNjAgMTYwIDE2MGg1OTUuMmwtOTIuOCA5Mi44Yy0xMi44IDEyLjgtMTIuOCAzMiAwIDQ0LjggMTIuOCAxMi44IDMyIDEyLjggNDQuOCAwbDE1Ni44LTE2MGM2LjQtNi40IDkuNi0xNiA5LjYtMjIuNCAwLTkuNi0zLjItMTYtOS42LTIyLjRsLTE1Ni44LTE1Ni44Yy0xMi44LTEyLjgtMzItMTIuOC00NC44IDAtMTIuOCAxMi44LTEyLjggMzIgMCA0NC44bDExNS4yIDExNS4yeiIgIGhvcml6LWFkdi14PSIxMDI0IiAvPgoKICAgIAoKCiAgPC9mb250Pgo8L2RlZnM+PC9zdmc+Cg=="},2322:t=>{t.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiID4KPCEtLQoyMDEzLTktMzA6IENyZWF0ZWQuCi0tPgo8c3ZnPgo8bWV0YWRhdGE+CkNyZWF0ZWQgYnkgaWNvbmZvbnQKPC9tZXRhZGF0YT4KPGRlZnM+Cgo8Zm9udCBpZD0iaWNvbmZvbnQiIGhvcml6LWFkdi14PSIxMDI0IiA+CiAgPGZvbnQtZmFjZQogICAgZm9udC1mYW1pbHk9Imljb25mb250IgogICAgZm9udC13ZWlnaHQ9IjUwMCIKICAgIGZvbnQtc3RyZXRjaD0ibm9ybWFsIgogICAgdW5pdHMtcGVyLWVtPSIxMDI0IgogICAgYXNjZW50PSI4OTYiCiAgICBkZXNjZW50PSItMTI4IgogIC8+CiAgICA8bWlzc2luZy1nbHlwaCAvPgogICAgCiAgICA8Z2x5cGggZ2x5cGgtbmFtZT0iYXJyb3dkb3duLWNvcHkiIHVuaWNvZGU9IiYjNTg5MTU7IiBkPSJNNzI2LjY1MyA0NjYuNjk0aC00MjkuMzA2bDIxNC44NDYtMjA4Ljg1MXoiICBob3Jpei1hZHYteD0iMTAyNCIgLz4KCiAgICAKICAgIDxnbHlwaCBnbHlwaC1uYW1lPSJwbGF5IiB1bmljb2RlPSImIzU5MTc0OyIgZD0iTTUxMiA4NTMuMzMyOTkyQzI1NC43MzMzMTIgODUzLjMzMjk5MiA0Mi42NjU5ODQgNjQxLjI2MzYxNiA0Mi42NjU5ODQgMzg0czIxMi4wNjczMjgtNDY5LjMzMTk2OCA0NjkuMzMxOTY4LTQ2OS4zMzE5NjhjMjU3LjI2NDY0IDAgNDY5LjMzNDAxNiAyMTIuMDY3MzI4IDQ2OS4zMzQwMTYgNDY5LjMzMTk2OCAwIDI1Ny4yNjQ2NC0yMTIuMDY5Mzc2IDQ2OS4zMzQwMTYtNDY5LjMzNDAxNiA0NjkuMzM0MDE2TTUxMi0xMjhDMjI4LjY5Mjk5Mi0xMjggMCAxMDAuNjkyOTkyIDAgMzg0IDAgNjY3LjMwNzAwOCAyMjguNjkyOTkyIDg5NiA1MTIgODk2YzI4My4zMDcwMDggMCA1MTItMjI4LjY5Mjk5MiA1MTItNTEyIDAtMjgzLjMwNzAwOC0yMjguNjkyOTkyLTUxMi01MTItNTEyTTYzMS44ODM3NzYgMzk5Ljg5NzZjMTAuNjE4ODgtNy45NDkzMTIgMTAuNjE4ODgtMjMuODQ2OTEyIDAtMzEuNzk1Mkw0MDUuMjM3NzYgMjE3LjA2NzUyQzM5NC42MTg4OCAyMDkuMTE5MjMyIDM4NCAyMTQuNDE3NDA4IDM4NCAyMjcuNjY1OTJWNTQwLjMzMjAzMmMwIDEzLjI0OTUzNiAxMC42MTk5MDQgMTguNTQ4NzM2IDIxLjIzNzc2IDEwLjYwMDQ0OGwyMjYuNjQ2MDE2LTE1MS4wMzQ4OHoiICBob3Jpei1hZHYteD0iMTAyNSIgLz4KCiAgICAKCgogIDwvZm9udD4KPC9kZWZzPjwvc3ZnPgo="},9379:t=>{t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAAAXNSR0IArs4c6QAAApJJREFUeAHt2T9OwzAUBvAatTC1lZIwsTJzAC7ACTgBLEhdOjGWDSYWVsQB6AVgR2wsCAkhsTI1ttQyIP4I87nCkpEiVJU0fkEfQ506afv847n2axoN/lGAAhSgAAUoQAEKUIACFKAABShAAQpQgAIUmFVAzXpheF2e5z2l1EuSJGdobXgu5vEozwtjWc2yucbpxrI0z4CsUtmntafamCtjzMY871GX18wF5Adnrd0E1M1I6+ORtW3f/5/aPwE5CCA18dBXxtxrrbf/E44by5+BPAig1pBN57nWF+PxeN33170tDchDAGrr7f39Dtk0wPGK769rWzrQN8QKsukA2eSgtuqK4+JeFJA3WQfUBaDOsTVY8511ahcNNLXAVHNf3u5LvI/jJoEKBLCDayObjnNjbgC1WXCJyK5KMujHyK3dANYV9k6nk8kk/XFO4JPqgYCAaabwsIPV7gHZtDN9LhDHhRQFyFsAJsW0E12yRAUKoMSWLCKAHNR0dRNYsogBCrJJVMkiDiiAElGyiAX6hopeskgH8gkVrZW+7X9tKHWUJckhftp9jaEkFgggl61ms9ftdh9jwPjPFAcEmCf8wt5P03Tog4zZigECzAdqtBObJINMqeeYKOFniwACzvWSUnu4jXQbBifhOCoQYDSm0760+2vhPyYKEGAsptPZcqu13+l0dBiQtOPqgZS6RdbsZWl6LQ2jKJ7KgIDyjMwZYDqdoP0oCkZiXyVAABm6m4tYup8kIvwW06KBHrE69QBz+VsQks8tCih6iVAWeulAmE4iSgRxQIARVSKIAXIrksQSQQQQcMSWCFGBsA/OgbMruUQoC4jvQwEKUIACFKAABShAAQpQgAIUoAAFKEABClBgdoEvgRgNiNlEUbkAAAAASUVORK5CYII="},5029:t=>{t.exports="data:application/vnd.ms-fontobject;base64,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"},4427:t=>{t.exports="data:font/ttf;base64,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"},1961:t=>{t.exports="data:application/vnd.ms-fontobject;base64,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"},6999:t=>{t.exports="data:font/ttf;base64,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"},3655:t=>{t.exports="data:font/woff;base64,d09GRgABAAAAAARoAAsAAAAABswAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABHU1VCAAABCAAAADMAAABCsP6z7U9TLzIAAAE8AAAARAAAAFY8mUkYY21hcAAAAYAAAABWAAABhmkn0RRnbHlmAAAB2AAAAJEAAACsoFCg7WhlYWQAAAJsAAAALwAAADYU+6wjaGhlYQAAApwAAAAeAAAAJAfeA4VobXR4AAACvAAAAAwAAAAMDAEAAGxvY2EAAALIAAAACAAAAAgADgBWbWF4cAAAAtAAAAAeAAAAIAEQADBuYW1lAAAC8AAAAUUAAAJtPlT+fXBvc3QAAAQ4AAAALgAAAD/D5yXjeJxjYGRgYOBikGPQYWB0cfMJYeBgYGGAAJAMY05meiJQDMoDyrGAaQ4gZoOIAgCKIwNPAHicY2BkYWCcwMDKwMHUyXSGgYGhH0IzvmYwYuRgYGBiYGVmwAoC0lxTGByeKT9XY27438AQw9zA0AAUZgTJAQDi6gwteJztkMENgDAMAy9p6QOxBRLiwUC8GJ+OUdwUtsDSxYqTl4EJSOIQGezC6DqVWuSJOfIcP0Vu+L3WrTX4XDLdSrhrFH4tMfd3897WoDdbtwH+ADzlDm4AAHicTYu7DcIwGITvt/FDjiIEQnFtRTItBQTmQGIBJskWbOQeUURykyI7pEKGP12uuu8eILDEQB8IgIIXQ7kzvZdYAr9+A9mjgkfLvYZp4DtcI2TEkQ2jhmwOW9Lxsjt3XiCVWSlyKZFTqswpl5E55EyBeXxaO9m6tvKxlKvx97RaZX6JyuzNSxtHN2c0/rNfLF8AAAB4nGNgZGBgAGLxnctexvPbfGXgZmEAgRvvTIQR9P8GFgbmBiCXg4EJJAoAMOgKOgB4nGNgZGBgbvjfwBDDAmQxMLAwMIBpJMAMAEciAm0AAAQAAAAEAAAABAEAAAAAAAAADgBWeJxjYGRgYGBmUAFiEGACYi4gZGD4D+YzAAAMZAFBAAB4nGWPTU7DMBCFX/oHpBKqqGCH5AViASj9EatuWFRq911036ZOmyqJI8et1ANwHo7ACTgC3IA78EgnmzaWx9+8eWNPANzgBx6O3y33kT1cMjtyDRe4F65TfxBukF+Em2jjVbhF/U3YxzOmwm10YXmD17hi9oR3YQ8dfAjXcI1P4Tr1L+EG+Vu4iTv8CrfQ8erCPuZeV7iNRy/2x1YvnF6p5UHFockikzm/gple75KFrdLqnGtbxCZTg6BfSVOdaVvdU+zXQ+ciFVmTqgmrOkmMyq3Z6tAFG+fyUa8XiR6EJuVYY/62xgKOcQWFJQ6MMUIYZIjK6Og7VWb0r7FDwl57Vj3N53RbFNT/c4UBAvTPXFO6stJ5Ok+BPV8bUnV0K27LnpQ0kV7NSRKyQl7WtlRC6gE2ZVeOEXpc0Yk/KGdI/wAJWm7IAAAAeJxjYGKAAC4G7ICZkYmRmZGFgS+xqCi/PCW/PE83Ob+gkqUgJ7GSgQEAYa8HrwAA"},9099:t=>{"use strict";t.exports="data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAALoAAsAAAAABswAAAKZAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDBgqBLIEoATYCJAMMCwgABCAFhG0HPxv3BciemjwJBBawWJiKrz0ASDwe/hv7dt/MfNW2KmnPdtG6CbVE49AhkkRLp5FIhAwheQn/z+kv/1pWQYEwj7X+Uuyl/nTcdSTtTi8NcwkH30gwCtRYSyHREE3ufj9P1OLQShNpd5+4d/rneReO/SzLZW3rorEmfwww7rwLcKxFEQ2RQLxh7AKXcJhA07wmodOhiRQqVFijAvHU1GVUmAsrCsvVhWrNwiLeqKinV+kQr8Pvx397KSSpZNbEyyeDGur5VflDWJ2tols+IUKAyyvI2IUU4k5t+hpBML6gaRfbS6uKkOaK/T+hXndWd/rHS0SVVd0Gm9CWbsrgqgh+00EB8vtVFGxALmYoJOTctiY+K0kvXH6mrr7SNz/Z6+9Zx5qJtFlNxSWTO6vj1HYuhJefJ9ftXh9abHpfLGqU77bG13rmtKH98PROBBAh724RzHCxLE2XTr/nplZqkQ6eox7Wz9KPuBAIal+/fLw7HX+FxQX8PFYXDpmtiii/QP1UnuDXdSohKNxkuXJR7VqrA9nqbYxMaGpihrB/6Hm8c9K7mlA3SZHUzCGrWyULu4uKln1U1R2gacfw8pYRRhClgW1bAKFvD0nXN2R9R2Rhn1Ax9YuqfgQ0XYmyDVvWY0r9DMsca6DMgalSjzhuLkW1CaxPOTLLmkJpBrPAjEEsHC3mhrGH2RxTgmk9zjkBwqgLQ+A47DgUfEYtrPKwwbnfFomQuheFVeoiyQkGk3GYBijmAJOK8hC/NSeVPp+A6aY4ZKyFU1PPwJiA2T2ICYv2IIftXi/OvTwTmKaL4zgCEAzlAkOgH+ZwUMCv72fBVFyYMSLpaxOxm5G+9vD6Tvf7tkGTNSlHihxF9z5jdEZzenvNKvXnRN+R5xACAAAA"},396:t=>{"use strict";t.exports="data:application/x-font-woff;charset=utf-8;base64,d09GRgABAAAAAAl8AAsAAAAADrgAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABHU1VCAAABCAAAADMAAABCsP6z7U9TLzIAAAE8AAAARAAAAFZW70yOY21hcAAAAYAAAAC4AAACPDvISv9nbHlmAAACOAAABNgAAAcsY+jklmhlYWQAAAcQAAAALgAAADYSO9jUaGhlYQAAB0AAAAAcAAAAJAfeA41obXR4AAAHXAAAABQAAAAwL+kAAGxvY2EAAAdwAAAAGgAAABoK2AjqbWF4cAAAB4wAAAAdAAAAIAEgAHJuYW1lAAAHrAAAAUUAAAJtPlT+fXBvc3QAAAj0AAAAhQAAAMBSkUPCeJxjYGRgYOBikGPQYWB0cfMJYeBgYGGAAJAMY05meiJQDMoDyrGAaQ4gZoOIAgCKIwNPAHicY2Bk/sc4gYGVgYOpk+kMAwNDP4RmfM1gxMjBwMDEwMrMgBUEpLmmMDgwVLyax9zwv4EhhrmBoREozAiSAwAxYQ0VeJzFkU0OgkAMhd/IiD+4cGGIh/AShEvARQxb7mBgx8KV9+EOJeES+DqFRI1rbfNN0jdJ27wCWAOIyIV4wD3goHGn6oIeYR90jyvrE45UPCpJJZNcSmmlH+qxmyb+LmohzYv6GY5dlkxxDqmq5zYrbLDFjjNjJLqZi790+FG4/41+j0N4b3OVkGqGK0pq6P0kM/Sukhv0FFIYdBdSGvQZ0hh0HNIaem/pDV4BQ23o3LEzED0Bp7w7HHicpVVNbBNXEH7zXvbX3nV2veuNndiJ7dhb4mStOM6aljpWSkNw1EIotVQOadNC4YCoBKUg2ormghJERKkKPREJaKVKARQhyAVIKFLVWy9w7qFqe2g5cODAxd501gZEEa3U1t43+97M7OzMNz9LOELWfmY3WQcJkxfIIHmVTBICfA5SKo1D0i46NAdmkjMtQ2V22k4K6ZTDymCleCNScItZixf4EKiQgKFkwbUdasNwcYRugEIkDhDtjG3XM106OwVyh5045k3QC2B2p7tCIwNetb9iFHrC4uGgrkd1fV7kOU6ktC2kwj4rInGSzHvfcKGYebN7He2GYNSOvbZD6enU350rfhDPWBLAzAyEO3vUbytaTMPr01gkrEeFdkXsiCnpXgMO/xroCAfj2V8I/iSfsN/o/eY+SmIkTYZIkZTJ64SE0yPossGnsplkio+DEfHjKUMxC09JVPg7yV+f2bsRsi9m8WKXGjVRlAR6UZDk+rGNkHkpgxdbbNSQwftsqf7Fc9n0Yty3kH1HEnb7zN2i6J1rsaZB4vf4vD285J3uavGaGqjLyxgfbcZK6CdIeEIyQ1paovRirebVYK935mNKPAL4b6mRtVvsNhvFfReJY/45wMymBAmymGbE2c91SQI/4Tawfd4dOKh09irevHdK7e1UYL8375/hIHzon715OK3D/ub2JBxAkep97p1U003VE03VQ00f1z5rI+woCWIuCJYctCegZwTaHQhziOqwVnQLFhcxKDlx7/jxexDxadn7idcUReMhzbOjx+95fzyWNR485AMS3BcD/EO+/QkGMy0MADEIgwakVjsDe+mMD8Car4QorN1ml1mFrMM6sEaglABLBcEBO+uWChaWNvLwhDwrwgtpBzDH7vS28oWp1QbH1Vd3LY509uTi4f4j43NXadvy3OiBvq2SpNRFOcICk+VNwNVXbtU52DKWGmfm4Hq2PDd7hUIhNylFAGSxrkjP+AGm0XxTBVovRpcKbskdSoDQcq3lZsmNsMsBsR6UxMnc/ldmlzl6bXb8SL8Rz3XHKou7VuttXGPl7fPlbbJJPfQHICJN5gpAr8zOLbP1gyYbT41tQf9uraB/m8qTUrNN5CZ2d+gS9ohNciSPnVIiG8g42UrewnpKprK2hi4k0QfTnwRl30/sD0FDTtKfDGzITJdwCY/u5qN7phVNE1KGD7kliy8hRZ7B07y3L5oByETpy7FeUEyFVgYa3w9UACoD8KWsKA8cZ+r69d/zeaRIbtyApYmdlO6cqPq06lYBQktdlWu9jruZti9lovBmy2AiEAwGEgMViha9S2hRMYPwUT6fH8O1yXGcr9FIy9TETu8uVN1011LIuYpjrOpaff+EyXby/n/DxMJlPweTxzVYyqZCWIXYhd3YfRVsy38HEMIzlc/T4LMIVV0Eh27uF3Vh26Coq8Io7t4T/hdaj+xWlwXhjUFB1cVRAU3qrT6/yxZZkSTJRkJKSYxuGDsbPxAYdtLyJ2geoRouYoA4TwXwMTP9uVoGH087i9rd+IkxjRAgt1j6gcb6hkFLaHTHWOPHQ7IRlc9ynHxWjhoytRtfPRHS7VNSOCovoHBBjoal72C4bzqkaaHpsR30kK++wAU5VVzwTcDE09IpX/8cp3CK1JT+CY0EVtZ4nGNgZGBgAGJHZyeFeH6brwzcLAwgcL3PKxdB/69nYWCuA3I5GJhAogDx5wjcAAB4nGNgZGBgbvjfwBDDwgACQJKRARXwAABHEgJ1eJxjYWBgYH7JwMDCQBwGACwHARkAAAAAAHYA7AECAT4BbgGCAcYCDAKWAzIDlgAAeJxjYGRgYOBhSGPgYgABJiDmArP/g/kMABRqAZMAAAB4nGWPTU7DMBCFX/oHpBKqqGCH5AViASj9EatuWFRq911036ZOmyqJI8et1ANwHo7ACTgC3IA78EgnmzaWx9+8eWNPANzgBx6O3y33kT1cMjtyDRe4F65TfxBukF+Em2jjVbhF/U3YxzOmwm10YXmD17hi9oR3YQ8dfAjXcI1P4Tr1L+EG+Vu4iTv8CrfQ8erCPuZeV7iNRy/2x1YvnF6p5UHFockikzm/gple75KFrdLqnGtbxCZTg6BfSVOdaVvdU+zXQ+ciFVmTqgmrOkmMyq3Z6tAFG+fyUa8XiR6EJuVYY/62xgKOcQWFJQ6MMUIYZIjK6Og7VWb0r7FDwl57Vj3N53RbFNT/c4UBAvTPXFO6stJ5Ok+BPV8bUnV0K27LnpQ0kV7NSRKyQl7WtlRC6gE2ZVeOEXpc0Yk/KGdI/wAJWm7IAAAAeJxtykEOgjAURdE+pIoIJrCPDlzSVz+lobRYWsXdK3FE4h2eXJGJX6X4X4UMO+SQ2OOAAkeUOKFCLbDIy5hsKubBTI6XKG/Wz5xPlt71amoK/DQ+zQ2F4F8q+EiRleUuthsJRvexvfqOnH4YdSc3Jp3INVsayJwdm+BX4v47CPEBESQxxgAAAA=="}},A={};function e(n){var i=A[n];if(void 0!==i)return i.exports;var o=A[n]={id:n,exports:{}};return t[n](o,o.exports,e),o.exports}e.m=t,e.n=t=>{var A=t&&t.__esModule?()=>t.default:()=>t;return e.d(A,{a:A}),A},e.d=(t,A)=>{for(var n in A)e.o(A,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:A[n]})},e.o=(t,A)=>Object.prototype.hasOwnProperty.call(t,A),e.b=document.baseURI||self.location.href,e.nc=void 0,(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(631),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(3525),y={};y.styleTagTransform=p(),y.setAttributes=l(),y.insert=a().bind(null,"head"),y.domAPI=i(),y.insertStyleElement=c(),A()(u.A,y),u.A&&u.A.locals&&u.A.locals;var g=e(52);(0,eval)('/**\n * Binary Search Stubs for JS Arrays\n * @license MIT\n * <AUTHOR> Chen\n */\nvar BinArray = (function(){\n\tvar BinArray = {};\n\tBinArray.bsearch = function(arr, what, how){\n\t\tif(arr.length === 0) {\n\t\t\treturn 0;\n\t\t}\n\t\tif(how(what,arr[0]) < 0) {\n\t\t\treturn 0;\n\t\t}\n\t\tif(how(what,arr[arr.length - 1]) >=0) {\n\t\t\treturn arr.length;\n\t\t}\n\t\tvar low =0;\n\t\tvar i = 0;\n\t\tvar count = 0;\n\t\tvar high = arr.length - 1;\n\t\twhile(low<=high){\n\t\t\ti = Math.floor((high + low + 1)/2);\n\t\t\tcount++;\n\t\t\tif(how(what,arr[i-1])>=0 && how(what,arr[i])<0){\n\t\t\t\treturn i;\n\t\t\t}\n\t\t\tif(how(what,arr[i-1])<0){\n\t\t\t\thigh = i-1;\n\t\t\t}else if(how(what,arr[i])>=0){\n\t\t\t\tlow = i;\n\t\t\t}else {\n\t\t\t\tconsole.error(\'Program Error\');\n\t\t\t}\n\t\t\tif(count > 1500) { console.error(\'Too many run cycles.\'); }\n\t\t}\n\t\treturn -1; // Never actually run\n\t};\n\tBinArray.binsert = function(arr, what, how){\n\t\tvar index = BinArray.bsearch(arr,what,how);\n\t\tarr.splice(index,0,what);\n\t\treturn index;\n\t};\n\treturn BinArray;\n})();\n\nvar __extends = (this && this.__extends) || function (d, b) {\n    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    function __() { this.constructor = d; }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n};\nvar CommentSpaceAllocator = (function () {\n    function CommentSpaceAllocator(width, height) {\n        if (width === void 0) { width = 0; }\n        if (height === void 0) { height = 0; }\n        this._pools = [\n            []\n        ];\n        this.avoid = 1;\n        this._width = width;\n        this._height = height;\n    }\n    CommentSpaceAllocator.prototype.willCollide = function (existing, check) {\n        return existing.stime + existing.ttl >= check.stime + check.ttl / 2;\n    };\n    CommentSpaceAllocator.prototype.pathCheck = function (y, comment, pool) {\n        var bottom = y + comment.height;\n        var right = comment.right;\n        for (var i = 0; i < pool.length; i++) {\n            if (pool[i].y > bottom || pool[i].bottom < y) {\n                continue;\n            }\n            else if (pool[i].right < comment.x || pool[i].x > right) {\n                if (this.willCollide(pool[i], comment)) {\n                    return false;\n                }\n                else {\n                    continue;\n                }\n            }\n            else {\n                return false;\n            }\n        }\n        return true;\n    };\n    CommentSpaceAllocator.prototype.assign = function (comment, cindex) {\n        while (this._pools.length <= cindex) {\n            this._pools.push([]);\n        }\n        var pool = this._pools[cindex];\n        if (pool.length === 0) {\n            comment.cindex = cindex;\n            return 0;\n        }\n        else if (this.pathCheck(0, comment, pool)) {\n            comment.cindex = cindex;\n            return 0;\n        }\n        var y = 0;\n        for (var k = 0; k < pool.length; k++) {\n            y = pool[k].bottom + this.avoid;\n            if (y + comment.height > this._height) {\n                break;\n            }\n            if (this.pathCheck(y, comment, pool)) {\n                comment.cindex = cindex;\n                return y;\n            }\n        }\n        return this.assign(comment, cindex + 1);\n    };\n    CommentSpaceAllocator.prototype.add = function (comment) {\n        if (comment.height > this._height) {\n            comment.cindex = -2;\n            comment.y = 0;\n        }\n        else {\n            comment.y = this.assign(comment, 0);\n            BinArray.binsert(this._pools[comment.cindex], comment, function (a, b) {\n                if (a.bottom < b.bottom) {\n                    return -1;\n                }\n                else if (a.bottom > b.bottom) {\n                    return 1;\n                }\n                else {\n                    return 0;\n                }\n            });\n        }\n    };\n    CommentSpaceAllocator.prototype.remove = function (comment) {\n        if (comment.cindex < 0) {\n            return;\n        }\n        if (comment.cindex >= this._pools.length) {\n            throw new Error("cindex out of bounds");\n        }\n        var index = this._pools[comment.cindex].indexOf(comment);\n        if (index < 0)\n            return;\n        this._pools[comment.cindex].splice(index, 1);\n    };\n    CommentSpaceAllocator.prototype.setBounds = function (width, height) {\n        this._width = width;\n        this._height = height;\n    };\n    return CommentSpaceAllocator;\n}());\nvar AnchorCommentSpaceAllocator = (function (_super) {\n    __extends(AnchorCommentSpaceAllocator, _super);\n    function AnchorCommentSpaceAllocator() {\n        _super.apply(this, arguments);\n    }\n    AnchorCommentSpaceAllocator.prototype.add = function (comment) {\n        _super.prototype.add.call(this, comment);\n        comment.x = (this._width - comment.width) / 2;\n    };\n    AnchorCommentSpaceAllocator.prototype.willCollide = function (a, b) {\n        return true;\n    };\n    AnchorCommentSpaceAllocator.prototype.pathCheck = function (y, comment, pool) {\n        var bottom = y + comment.height;\n        for (var i = 0; i < pool.length; i++) {\n            if (pool[i].y > bottom || pool[i].bottom < y) {\n                continue;\n            }\n            else {\n                return false;\n            }\n        }\n        return true;\n    };\n    return AnchorCommentSpaceAllocator;\n}(CommentSpaceAllocator));\n//# sourceMappingURL=CommentSpaceAllocator.js.map\nvar __extends = (this && this.__extends) || function (d, b) {\n    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    function __() { this.constructor = d; }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n};\nvar CoreComment = (function () {\n    function CoreComment(parent, init) {\n        if (init === void 0) { init = {}; }\n        this.mode = 1;\n        this.stime = 0;\n        this.text = "";\n        this.ttl = 4000;\n        this.dur = 4000;\n        this.cindex = -1;\n        this.motion = [];\n        this.movable = true;\n        this._alphaMotion = null;\n        this.absolute = true;\n        this.align = 0;\n        this._alpha = 1;\n        this._size = 25;\n        this._color = 0xffffff;\n        this._border = false;\n        this._shadow = true;\n        this._font = "";\n        if (!parent) {\n            throw new Error("Comment not bound to comment manager.");\n        }\n        else {\n            this.parent = parent;\n        }\n        if (init.hasOwnProperty("stime")) {\n            this.stime = init["stime"];\n        }\n        if (init.hasOwnProperty("mode")) {\n            this.mode = init["mode"];\n        }\n        else {\n            this.mode = 1;\n        }\n        if (init.hasOwnProperty("dur")) {\n            this.dur = init["dur"];\n            this.ttl = this.dur;\n        }\n        this.dur *= this.parent.options.global.scale;\n        this.ttl *= this.parent.options.global.scale;\n        if (init.hasOwnProperty("text")) {\n            this.text = init["text"];\n        }\n        if (init.hasOwnProperty("motion")) {\n            this._motionStart = [];\n            this._motionEnd = [];\n            this.motion = init["motion"];\n            var head = 0;\n            for (var i = 0; i < init["motion"].length; i++) {\n                this._motionStart.push(head);\n                var maxDur = 0;\n                for (var k in init["motion"][i]) {\n                    var m = init["motion"][i][k];\n                    maxDur = Math.max(m.dur, maxDur);\n                    if (m.easing === null || m.easing === undefined) {\n                        init["motion"][i][k]["easing"] = CoreComment.LINEAR;\n                    }\n                }\n                head += maxDur;\n                this._motionEnd.push(head);\n            }\n            this._curMotion = 0;\n        }\n        if (init.hasOwnProperty("color")) {\n            this._color = init["color"];\n        }\n        if (init.hasOwnProperty("size")) {\n            this._size = init["size"];\n        }\n        if (init.hasOwnProperty("border")) {\n            this._border = init["border"];\n        }\n        if (init.hasOwnProperty("opacity")) {\n            this._alpha = init["opacity"];\n        }\n        if (init.hasOwnProperty("alpha")) {\n            this._alphaMotion = init["alpha"];\n        }\n        if (init.hasOwnProperty("font")) {\n            this._font = init["font"];\n        }\n        if (init.hasOwnProperty("x")) {\n            this._x = init["x"];\n        }\n        if (init.hasOwnProperty("y")) {\n            this._y = init["y"];\n        }\n        if (init.hasOwnProperty("shadow")) {\n            this._shadow = init["shadow"];\n        }\n        if (init.hasOwnProperty("position")) {\n            if (init["position"] === "relative") {\n                this.absolute = false;\n                if (this.mode < 7) {\n                    console.warn("Using relative position for CSA comment.");\n                }\n            }\n        }\n    }\n    CoreComment.prototype.init = function (recycle) {\n        if (recycle === void 0) { recycle = null; }\n        if (recycle !== null) {\n            this.dom = recycle.dom;\n        }\n        else {\n            this.dom = document.createElement("div");\n        }\n        this.dom.className = this.parent.options.global.className;\n        this.dom.appendChild(document.createTextNode(this.text));\n        this.dom.textContent = this.text;\n        this.dom.innerText = this.text;\n        this.size = this._size;\n        if (this._color != 0xffffff) {\n            this.color = this._color;\n        }\n        this.shadow = this._shadow;\n        if (this._border) {\n            this.border = this._border;\n        }\n        if (this._font !== "") {\n            this.font = this._font;\n        }\n        if (this._x !== undefined) {\n            this.x = this._x;\n        }\n        if (this._y !== undefined) {\n            this.y = this._y;\n        }\n        if (this._alpha !== 1 || this.parent.options.global.opacity < 1) {\n            this.alpha = this._alpha;\n        }\n        if (this.motion.length > 0) {\n            this.animate();\n        }\n    };\n    Object.defineProperty(CoreComment.prototype, "x", {\n        get: function () {\n            if (this._x === null || this._x === undefined) {\n                if (this.align % 2 === 0) {\n                    this._x = this.dom.offsetLeft;\n                }\n                else {\n                    this._x = this.parent.width - this.dom.offsetLeft - this.width;\n                }\n            }\n            if (!this.absolute) {\n                return this._x / this.parent.width;\n            }\n            return this._x;\n        },\n        set: function (x) {\n            this._x = x;\n            if (!this.absolute) {\n                this._x *= this.parent.width;\n            }\n            if (this.align % 2 === 0) {\n                this.dom.style.left = this._x + "px";\n            }\n            else {\n                this.dom.style.right = this._x + "px";\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "y", {\n        get: function () {\n            if (this._y === null || this._y === undefined) {\n                if (this.align < 2) {\n                    this._y = this.dom.offsetTop;\n                }\n                else {\n                    this._y = this.parent.height - this.dom.offsetTop - this.height;\n                }\n            }\n            if (!this.absolute) {\n                return this._y / this.parent.height;\n            }\n            return this._y;\n        },\n        set: function (y) {\n            this._y = y;\n            if (!this.absolute) {\n                this._y *= this.parent.height;\n            }\n            if (this.align < 2) {\n                this.dom.style.top = this._y + "px";\n            }\n            else {\n                this.dom.style.bottom = this._y + "px";\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "bottom", {\n        get: function () {\n            return this.y + this.height;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "right", {\n        get: function () {\n            return this.x + this.width;\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "width", {\n        get: function () {\n            if (this._width === null || this._width === undefined) {\n                this._width = this.dom.offsetWidth;\n            }\n            return this._width;\n        },\n        set: function (w) {\n            this._width = w;\n            this.dom.style.width = this._width + "px";\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "height", {\n        get: function () {\n            if (this._height === null || this._height === undefined) {\n                this._height = this.dom.offsetHeight;\n            }\n            return this._height;\n        },\n        set: function (h) {\n            this._height = h;\n            this.dom.style.height = this._height + "px";\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "size", {\n        get: function () {\n            return this._size;\n        },\n        set: function (s) {\n            this._size = s;\n            this.dom.style.fontSize = this._size + "px";\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "color", {\n        get: function () {\n            return this._color;\n        },\n        set: function (c) {\n            this._color = c;\n            var color = c.toString(16);\n            color = color.length >= 6 ? color : new Array(6 - color.length + 1).join("0") + color;\n            this.dom.style.color = "#" + color;\n            if (this._color === 0) {\n                this.dom.className = this.parent.options.global.className + " rshadow";\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "alpha", {\n        get: function () {\n            return this._alpha;\n        },\n        set: function (a) {\n            this._alpha = a;\n            this.dom.style.opacity = Math.min(this._alpha, this.parent.options.global.opacity) + "";\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "border", {\n        get: function () {\n            return this._border;\n        },\n        set: function (b) {\n            this._border = b;\n            if (this._border) {\n                this.dom.style.border = "1px solid #00ffff";\n            }\n            else {\n                this.dom.style.border = "none";\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "shadow", {\n        get: function () {\n            return this._shadow;\n        },\n        set: function (s) {\n            this._shadow = s;\n            if (!this._shadow) {\n                this.dom.className = this.parent.options.global.className + " noshadow";\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(CoreComment.prototype, "font", {\n        get: function () {\n            return this._font;\n        },\n        set: function (f) {\n            this._font = f;\n            if (this._font.length > 0) {\n                this.dom.style.fontFamily = this._font;\n            }\n            else {\n                this.dom.style.fontFamily = "";\n            }\n        },\n        enumerable: true,\n        configurable: true\n    });\n    CoreComment.prototype.time = function (time) {\n        this.ttl -= time;\n        if (this.ttl < 0) {\n            this.ttl = 0;\n        }\n        if (this.movable) {\n            this.update();\n        }\n        if (this.ttl <= 0) {\n            this.finish();\n        }\n    };\n    CoreComment.prototype.update = function () {\n        this.animate();\n    };\n    CoreComment.prototype.invalidate = function () {\n        this._x = null;\n        this._y = null;\n        this._width = null;\n        this._height = null;\n    };\n    CoreComment.prototype._execMotion = function (currentMotion, time) {\n        for (var prop in currentMotion) {\n            if (currentMotion.hasOwnProperty(prop)) {\n                var m = currentMotion[prop];\n                this[prop] = m.easing(Math.min(Math.max(time - m.delay, 0), m.dur), m.from, m.to - m.from, m.dur);\n            }\n        }\n    };\n    CoreComment.prototype.animate = function () {\n        if (this._alphaMotion) {\n            this.alpha = (this.dur - this.ttl) * (this._alphaMotion["to"] - this._alphaMotion["from"]) / this.dur + this._alphaMotion["from"];\n        }\n        if (this.motion.length === 0) {\n            return;\n        }\n        var ttl = Math.max(this.ttl, 0);\n        var time = (this.dur - ttl) - this._motionStart[this._curMotion];\n        this._execMotion(this.motion[this._curMotion], time);\n        if (this.dur - ttl > this._motionEnd[this._curMotion]) {\n            this._curMotion++;\n            if (this._curMotion >= this.motion.length) {\n                this._curMotion = this.motion.length - 1;\n            }\n            return;\n        }\n    };\n    CoreComment.prototype.finish = function () {\n        this.parent.finish(this);\n    };\n    CoreComment.prototype.toString = function () {\n        return ["[", this.stime, "|", this.ttl, "/", this.dur, "]", "(", this.mode, ")", this.text].join("");\n    };\n    CoreComment.LINEAR = function (t, b, c, d) {\n        return t * c / d + b;\n    };\n    return CoreComment;\n}());\nvar ScrollComment = (function (_super) {\n    __extends(ScrollComment, _super);\n    function ScrollComment(parent, data) {\n        _super.call(this, parent, data);\n        this.dur *= this.parent.options.scroll.scale;\n        this.ttl *= this.parent.options.scroll.scale;\n    }\n    Object.defineProperty(ScrollComment.prototype, "alpha", {\n        set: function (a) {\n            this._alpha = a;\n            this.dom.style.opacity = Math.min(Math.min(this._alpha, this.parent.options.global.opacity), this.parent.options.scroll.opacity) + "";\n        },\n        enumerable: true,\n        configurable: true\n    });\n    ScrollComment.prototype.init = function (recycle) {\n        if (recycle === void 0) { recycle = null; }\n        _super.prototype.init.call(this, recycle);\n        this.x = this.parent.width;\n        if (this.parent.options.scroll.opacity < 1) {\n            this.alpha = this._alpha;\n        }\n        this.absolute = true;\n    };\n    ScrollComment.prototype.update = function () {\n        this.x = (this.ttl / this.dur) * (this.parent.width + this.width) - this.width;\n    };\n    return ScrollComment;\n}(CoreComment));\n//# sourceMappingURL=Comment.js.map\n/** \n * Comment Filters Module Simplified (only supports modifiers & types)\n * @license MIT\n * <AUTHOR> Chen\n */\nfunction CommentFilter(){\n\tthis.modifiers = [];\n\tthis.runtime = null;\n\tthis.allowTypes = {\n\t\t"1":true,\n\t\t"4":true,\n\t\t"5":true,\n\t\t"6":true,\n\t\t"7":true,\n\t\t"8":true,\n\t\t"17":true\n\t};\n\tthis.doModify = function(cmt){\n\t\tfor(var k=0;k<this.modifiers.length;k++){\n\t\t\tcmt = this.modifiers[k](cmt);\n\t\t}\n\t\treturn cmt;\n\t};\n\tthis.beforeSend = function(cmt){\n\t\treturn cmt;\n\t}\n\tthis.doValidate = function(cmtData){\n\t\tif(!this.allowTypes[cmtData.mode])\n\t\t\treturn false;\n\t\treturn true;\n\t};\n\tthis.addRule = function(rule){\n\t\t\n\t};\n\tthis.addModifier = function(f){\n\t\tthis.modifiers.push(f);\n\t};\n\tthis.runtimeFilter = function(cmt){\n\t\tif(this.runtime == null)\n\t\t\treturn cmt;\n\t\treturn this.runtime(cmt);\n\t};\n\tthis.setRuntimeFilter = function(f){\n\t\tthis.runtime = f;\n\t}\n}\n\n/*!\n * Comment Core Library CommentManager\n * @license MIT\n * <AUTHOR> Chen\n *\n * Copyright (c) 2014 Jim Chen\n */\nvar CommentManager = (function() {\n\tvar getRotMatrix = function(yrot, zrot) {\n\t\t// Courtesy of @StarBrilliant, re-adapted to look better\n\t\tvar DEG2RAD = Math.PI/180;\n\t\tvar yr = yrot * DEG2RAD;\n\t\tvar zr = zrot * DEG2RAD;\n\t\tvar COS = Math.cos;\n\t\tvar SIN = Math.sin;\n\t\tvar matrix = [\n\t\t\tCOS(yr) * COS(zr)    , COS(yr) * SIN(zr)     , SIN(yr)  , 0,\n\t\t\t(-SIN(zr))           , COS(zr)               , 0        , 0,\n\t\t\t(-SIN(yr) * COS(zr)) , (-SIN(yr) * SIN(zr))  , COS(yr)  , 0,\n\t\t\t0                    , 0                     , 0        , 1\n\t\t];\n\t\t// CSS does not recognize scientific notation (e.g. 1e-6), truncating it.\n\t\tfor(var i = 0; i < matrix.length;i++){\n\t\t\tif(Math.abs(matrix[i]) < 0.000001){\n\t\t\t\tmatrix[i] = 0;\n\t\t\t}\n\t\t}\n\t\treturn "matrix3d(" + matrix.join(",") + ")";\n\t};\n\n\tfunction CommentManager(stageObject){\n\t\tvar __timer = 0;\n\t\t\n\t\tthis._listeners = {};\n\t\tthis._lastPosition = 0;\n\t\t\n\t\tthis.stage = stageObject;\n\t\tthis.options = {\n\t\t\tglobal:{\n\t\t\t\topacity:1,\n\t\t\t\tscale:1,\n\t\t\t\tclassName:"cmt"\n\t\t\t},\n\t\t\tscroll:{\n\t\t\t\topacity:1,\n\t\t\t\tscale:1\n\t\t\t},\n\t\t\tlimit: 0\n\t\t};\n\t\tthis.timeline = [];\n\t\tthis.runline = [];\n\t\tthis.position = 0;\n\t\tthis.limiter = 0;\n\t\tthis.filter = null;\n\t\tthis.csa = {\n\t\t\tscroll: new CommentSpaceAllocator(0,0),\n\t\t\ttop:new AnchorCommentSpaceAllocator(0,0),\n\t\t\tbottom:new AnchorCommentSpaceAllocator(0,0),\n\t\t\treverse:new CommentSpaceAllocator(0,0),\n\t\t\tscrollbtm:new CommentSpaceAllocator(0,0)\n\t\t};\n\t\t\n\t\t/** Precompute the offset width **/\n\t\tthis.width = this.stage.offsetWidth;\n\t\tthis.height = this.stage.offsetHeight;\n\t\tthis.startTimer = function(){\n\t\t\tif(__timer > 0)\n\t\t\t\treturn;\n\t\t\tvar lastTPos = new Date().getTime();\n\t\t\tvar cmMgr = this;\n\t\t\t__timer = window.setInterval(function(){\n\t\t\t\tvar elapsed = new Date().getTime() - lastTPos;\n\t\t\t\tlastTPos = new Date().getTime();\n\t\t\t\tcmMgr.onTimerEvent(elapsed,cmMgr);\n\t\t\t},10);\n\t\t};\n\t\tthis.stopTimer = function(){\n\t\t\twindow.clearInterval(__timer);\n\t\t\t__timer = 0;\n\t\t};\n\t}\n\n\t/** Public **/\n\tCommentManager.prototype.stop = function(){\n\t\tthis.stopTimer();\n\t};\n\n\tCommentManager.prototype.start = function(){\n\t\tthis.startTimer();\n\t};\n\n\tCommentManager.prototype.seek = function(time){\n\t\tthis.position = BinArray.bsearch(this.timeline, time, function(a,b){\n\t\t\tif(a < b.stime) return -1\n\t\t\telse if(a > b.stime) return 1;\n\t\t\telse return 0;\n\t\t});\n\t};\n\n\tCommentManager.prototype.validate = function(cmt){\n\t\tif(cmt == null)\n\t\t\treturn false;\n\t\treturn this.filter.doValidate(cmt);\n\t};\n\n\tCommentManager.prototype.load = function(a){\n\t\tthis.timeline = a;\n\t\tthis.timeline.sort(function(a,b){\n\t\t\tif(a.stime > b.stime) return 2;\n\t\t\telse if(a.stime < b.stime) return -2;\n\t\t\telse{\n\t\t\t\tif(a.date > b.date) return 1;\n\t\t\t\telse if(a.date < b.date) return -1;\n\t\t\t\telse if(a.dbid != null && b.dbid != null){\n\t\t\t\t\tif(a.dbid > b.dbid) return 1;\n\t\t\t\t\telse if(a.dbid < b.dbid) return -1;\n\t\t\t\t\treturn 0;\n\t\t\t\t}else\n\t\t\t\t\treturn 0;\n\t\t\t}\n\t\t});\n\t\tthis.dispatchEvent("load");\n\t};\n\n\tCommentManager.prototype.insert = function(c){\n\t\tvar index = BinArray.binsert(this.timeline, c, function(a,b){\n\t\t\tif(a.stime > b.stime) return 2;\n\t\t\telse if(a.stime < b.stime) return -2;\n\t\t\telse{\n\t\t\t\tif(a.date > b.date) return 1;\n\t\t\t\telse if(a.date < b.date) return -1;\n\t\t\t\telse if(a.dbid != null && b.dbid != null){\n\t\t\t\t\tif(a.dbid > b.dbid) return 1;\n\t\t\t\t\telse if(a.dbid < b.dbid) return -1;\n\t\t\t\t\treturn 0;\n\t\t\t\t}else\n\t\t\t\t\treturn 0;\n\t\t\t}\n\t\t});\n\t\tif(index <= this.position){\n\t\t\tthis.position++;\n\t\t}\n\t\tthis.dispatchEvent("insert");\n\t};\n\n\tCommentManager.prototype.clear = function(){\n\t\twhile(this.runline.length > 0){\n\t\t\tthis.runline[0].finish();\n\t\t}\n\t\tthis.dispatchEvent("clear");\n\t};\n\n\tCommentManager.prototype.setBounds = function(){\n\t\tthis.width = this.stage.offsetWidth;\n\t\tthis.height= this.stage.offsetHeight;\n\t\tthis.dispatchEvent("resize");\n\t\tfor(var comAlloc in this.csa){\n\t\t\tthis.csa[comAlloc].setBounds(this.width,this.height);\n\t\t}\n\t\t// Update 3d perspective\n\t\tthis.stage.style.perspective = this.width * Math.tan(40 * Math.PI/180) / 2 + "px";\n\t\tthis.stage.style.webkitPerspective = this.width * Math.tan(40 * Math.PI/180) / 2 + "px";\n\t};\n\tCommentManager.prototype.init = function(){\n\t\tthis.setBounds();\n\t\tif(this.filter == null) {\n\t\t\tthis.filter = new CommentFilter(); //Only create a filter if none exist\n\t\t}\n\t};\n\tCommentManager.prototype.time = function(time){\n\t\ttime = time - 1;\n\t\tif(this.position >= this.timeline.length || Math.abs(this._lastPosition - time) >= 2000){\n\t\t\tthis.seek(time);\n\t\t\tthis._lastPosition = time;\n\t\t\tif(this.timeline.length <= this.position) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t}else{\n\t\t\tthis._lastPosition = time;\n\t\t}\n\t\tfor(;this.position < this.timeline.length;this.position++){\n\t\t\tif(this.timeline[this.position][\'stime\']<=time){\n\t\t\t\tif(this.options.limit > 0 && this.runline.length > this.limiter) {\n\t\t\t\t\tcontinue; // Skip comments but still move the position pointer\n\t\t\t\t} else if(this.validate(this.timeline[this.position])){\n\t\t\t\t\tthis.send(this.timeline[this.position]);\n\t\t\t\t}\n\t\t\t}else{\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t};\n\tCommentManager.prototype.rescale = function(){\n\t\t\n\t};\n\tCommentManager.prototype.send = function(data){\n\t\tif(data.mode === 8){\n\t\t\tconsole.log(data);\n\t\t\tif(this.scripting){\n\t\t\t\tconsole.log(this.scripting.eval(data.code));\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\t\tif(this.filter != null){\n\t\t\tdata = this.filter.doModify(data);\n\t\t\tif(data == null) return;\n\t\t}\n\t\tif(data.mode === 1 || data.mode === 2 || data.mode === 6){\n\t\t\tvar cmt = new ScrollComment(this, data);\n\t\t}else{\n\t\t\tvar cmt = new CoreComment(this, data);\n\t\t}\n\t\tswitch(cmt.mode){\n\t\t\tcase 1:cmt.align = 0;break;\n\t\t\tcase 2:cmt.align = 2;break;\n\t\t\tcase 4:cmt.align = 2;break;\n\t\t\tcase 5:cmt.align = 0;break;\n\t\t\tcase 6:cmt.align = 1;break;\n\t\t}\n\t\tcmt.init();\n\t\tthis.stage.appendChild(cmt.dom);\n\t\tswitch(cmt.mode){\n\t\t\tdefault:\n\t\t\tcase 1:{this.csa.scroll.add(cmt);}break;\n\t\t\tcase 2:{this.csa.scrollbtm.add(cmt);}break;\n\t\t\tcase 4:{this.csa.bottom.add(cmt);}break;\n\t\t\tcase 5:{this.csa.top.add(cmt);}break;\n\t\t\tcase 6:{this.csa.reverse.add(cmt);}break;\n\t\t\tcase 17:\n\t\t\tcase 7:{\n\t\t\t\tif(data.rY !== 0 || data.rZ !== 0){\n\t\t\t\t\t/** TODO: revise when browser manufacturers make up their mind on Transform APIs **/\n\t\t\t\t\tcmt.dom.style.transform = getRotMatrix(data.rY, data.rZ);\n\t\t\t\t\tcmt.dom.style.webkitTransform = getRotMatrix(data.rY, data.rZ);\n\t\t\t\t\tcmt.dom.style.OTransform = getRotMatrix(data.rY, data.rZ);\n\t\t\t\t\tcmt.dom.style.MozTransform = getRotMatrix(data.rY, data.rZ);\n\t\t\t\t\tcmt.dom.style.MSTransform = getRotMatrix(data.rY, data.rZ);\n\t\t\t\t}\n\t\t\t}break;\n\t\t}\n\t\tcmt.y = cmt.y;\n\t\tthis.dispatchEvent("enterComment", cmt);\n\t\tthis.runline.push(cmt);\n\t};\n\tCommentManager.prototype.sendComment = function(data){\n\t\tconsole.log("CommentManager.sendComment is deprecated. Please use send instead");\n\t\tthis.send(data); // Wrapper for Backwards Compatible APIs\n\t};\n\tCommentManager.prototype.finish = function(cmt){\n\t\tthis.dispatchEvent("exitComment", cmt);\n\t\tthis.stage.removeChild(cmt.dom);\n\t\tvar index = this.runline.indexOf(cmt);\n\t\tif(index >= 0){\n\t\t\tthis.runline.splice(index, 1);\n\t\t}\n\t\tswitch(cmt.mode){\n\t\t\tdefault:\n\t\t\tcase 1:{this.csa.scroll.remove(cmt);}break;\n\t\t\tcase 2:{this.csa.scrollbtm.remove(cmt);}break;\n\t\t\tcase 4:{this.csa.bottom.remove(cmt);}break;\n\t\t\tcase 5:{this.csa.top.remove(cmt);}break;\n\t\t\tcase 6:{this.csa.reverse.remove(cmt);}break;\n\t\t\tcase 7:break;\n\t\t}\n\t};\n\tCommentManager.prototype.addEventListener = function(event, listener){\n\t\tif(typeof this._listeners[event] !== "undefined"){\n\t\t\tthis._listeners[event].push(listener);\n\t\t}else{\n\t\t\tthis._listeners[event] = [listener];\n\t\t}\n\t};\n\tCommentManager.prototype.dispatchEvent = function(event, data){\n\t\tif(typeof this._listeners[event] !== "undefined"){\n\t\t\tfor(var i = 0; i < this._listeners[event].length; i++){\n\t\t\t\ttry{\n\t\t\t\t\tthis._listeners[event][i](data);\n\t\t\t\t}catch(e){\n\t\t\t\t\tconsole.err(e.stack);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n\t/** Static Functions **/\n\tCommentManager.prototype.onTimerEvent = function(timePassed,cmObj){\n\t\tfor(var i= 0;i < cmObj.runline.length; i++){\n\t\t\tvar cmt = cmObj.runline[i];\n\t\t\tif(cmt.hold){\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tcmt.time(timePassed);\n\t\t}\n\t};\n\treturn CommentManager;\n})();\n\n/** \n * AcFun Format Parser\n * @license MIT License\n * An alternative format comment parser\n */\nfunction AcfunParser(jsond){\n\tvar list = [];\n\ttry{\n\t\tvar jsondt = JSON.parse(jsond);\n\t}catch(e){\n\t\tconsole.log(\'Error: Could not parse json list!\');\n\t\treturn [];\n\t}\n\tfor(var i=0;i<jsondt.length;i++){\n\t\t//Read each comment and generate a correct comment object\n\t\tvar data = {};\n\t\tvar xc = jsondt[i][\'c\'].split(\',\');\n\t\tif(xc.length > 0){\n\t\t\tdata.stime = parseFloat(xc[0]) * 1000;\n\t\t\tdata.color = parseInt(xc[1])\n\t\t\tdata.mode = parseInt(xc[2]);\n\t\t\tdata.size = parseInt(xc[3]);\n\t\t\tdata.hash = xc[4];\n\t\t\tdata.date = parseInt(xc[5]);\n\t\t\tdata.position = "absolute";\n\t\t\tif(data.mode != 7){\n\t\t\t\tdata.text = jsondt[i].m.replace(/(\\/n|\\\\n|\\n|\\r\\n|\\\\r)/g,"\\n");\n\t\t\t\tdata.text = data.text.replace(/\\r/g,"\\n");\n\t\t\t\tdata.text = data.text.replace(/\\s/g,"\\u00a0");\n\t\t\t}else{\n\t\t\t\tdata.text = jsondt[i].m;\n\t\t\t}\n\t\t\tif(data.mode == 7){\n\t\t\t\t//High level positioned dm\n\t\t\t\ttry{\n\t\t\t\t\tvar x = JSON.parse(data.text);\n\t\t\t\t}catch(e){\n\t\t\t\t\tconsole.log(\'[Err] Error parsing internal data for comment\');\n\t\t\t\t\tconsole.log(\'[Dbg] \' + data.text);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tdata.position = "relative";\n\t\t\t\tdata.text = x.n; /*.replace(/\\r/g,"\\n");*/\n\t\t\t\tdata.text = data.text.replace(/\\ /g,"\\u00a0");\n\t\t\t\tif(x.a != null){\n\t\t\t\t\tdata.opacity = x.a;\n\t\t\t\t}else{\n\t\t\t\t\tdata.opacity = 1;\n\t\t\t\t}\n\t\t\t\tif(x.p != null){\n\t\t\t\t\tdata.x = x.p.x / 1000; // relative position\n\t\t\t\t\tdata.y = x.p.y / 1000;\n\t\t\t\t}else{\n\t\t\t\t\tdata.x = 0;\n\t\t\t\t\tdata.y = 0;\n\t\t\t\t}\n\t\t\t\tdata.shadow = x.b;\n\t\t\t\tdata.dur = 4000;\n\t\t\t\tif(x.l != null)\n\t\t\t\t\tdata.moveDelay = x.l * 1000;\n\t\t\t\tif(x.z != null && x.z.length > 0){\n\t\t\t\t\tdata.movable = true;\n\t\t\t\t\tdata.motion = [];\n\t\t\t\t\tvar moveDuration = 0;\n\t\t\t\t\tvar last = {x:data.x, y:data.y, alpha:data.opacity, color:data.color};\n\t\t\t\t\tfor(var m = 0; m < x.z.length; m++){\n\t\t\t\t\t\tvar dur = x.z[m].l != null ? (x.z[m].l * 1000) : 500;\n\t\t\t\t\t\tmoveDuration += dur;\n\t\t\t\t\t\tvar motion = {\n\t\t\t\t\t\t\tx:{from:last.x, to:x.z[m].x/1000, dur: dur, delay: 0},\n\t\t\t\t\t\t\ty:{from:last.y, to:x.z[m].y/1000, dur: dur, delay: 0}\n\t\t\t\t\t\t};\n\t\t\t\t\t\tlast.x = motion.x.to;\n\t\t\t\t\t\tlast.y = motion.y.to;\n\t\t\t\t\t\tif(x.z[m].t !== last.alpha){\n\t\t\t\t\t\t\tmotion.alpha = {from:last.alpha, to:x.z[m].t, dur: dur, delay: 0};\n\t\t\t\t\t\t\tlast.alpha = motion.alpha.to;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(x.z[m].c != null && x.z[m].c !== last.color){\n\t\t\t\t\t\t\tmotion.color = {from:last.color, to:x.z[m].c, dur: dur, delay: 0};\n\t\t\t\t\t\t\tlast.color = motion.color.to;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdata.motion.push(motion);\n\t\t\t\t\t}\n\t\t\t\t\tdata.dur = moveDuration + (data.moveDelay ? data.moveDelay : 0);\n\t\t\t\t}\n\t\t\t\tif(x.r != null && x.k != null){\n\t\t\t\t\tdata.rX = x.r;\n\t\t\t\t\tdata.rY = x.k;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t}\n\t\t\tlist.push(data);\n\t\t}\n\t}\n\treturn list;\n}\n\n/** \n * Bilibili Format Parser\n * @license MIT License\n * Takes in an XMLDoc/LooseXMLDoc and parses that into a Generic Comment List\n **/\nfunction BilibiliParser(xmlDoc, text, warn){\t\n\tfunction format(string){\n\t\t// Format the comment text to be JSON Valid.\n\t\treturn string.replace(/\\t/,"\\\\t");\t\n\t}\n\t\n\tif(xmlDoc !== null){\n\t\tvar elems = xmlDoc.getElementsByTagName(\'d\');\n\t}else{\n\t\tif(!document || !document.createElement){\n\t\t\t// Maybe we are in a restricted context? Bail.\n\t\t\treturn [];\n\t\t}\n\t\tif(warn){\n\t\t\tif(!confirm("XML Parse Error. \\n Allow tag soup parsing?\\n[WARNING: This is unsafe.]")){\n\t\t\t\treturn [];\n\t\t\t}\n\t\t}else{\n\t\t\t// TODO: Make this safer in the future\n\t\t\ttext = text.replace(new RegExp("</([^d])","g"), "</disabled $1");\n\t\t\ttext = text.replace(new RegExp("</(\\S{2,})","g"), "</disabled $1");\n\t\t\ttext = text.replace(new RegExp("<([^d/]\\W*?)","g"), "<disabled $1");\n\t\t\ttext = text.replace(new RegExp("<([^/ ]{2,}\\W*?)","g"), "<disabled $1");\n\t\t}\n\t\tvar tmp = document.createElement("div");\n\t\ttmp.innerHTML = text;\n\t\tvar elems = tmp.getElementsByTagName(\'d\');\n\t}\n\t\n\tvar tlist = [];\n\tfor(var i=0;i < elems.length;i++){\n\t\tif(elems[i].getAttribute(\'p\') != null){\n\t\t\tvar opt = elems[i].getAttribute(\'p\').split(\',\');\n\t\t\tif(!elems[i].childNodes[0])\n\t\t\t  continue;\n\t\t\tvar text = elems[i].childNodes[0].nodeValue;\n\t\t\tvar obj = {};\n\t\t\tobj.stime = Math.round(parseFloat(opt[0])*1000);\n\t\t\tobj.size = parseInt(opt[2]);\n\t\t\tobj.color = parseInt(opt[3]);\n\t\t\tobj.mode = parseInt(opt[1]);\n\t\t\tobj.date = parseInt(opt[4]);\n\t\t\tobj.pool = parseInt(opt[5]);\n\t\t\tobj.position = "absolute";\n\t\t\tif(opt[7] != null)\n\t\t\t\tobj.dbid = parseInt(opt[7]);\n\t\t\tobj.hash = opt[6];\n\t\t\tobj.border = false;\n\t\t\tif(obj.mode < 7){\n\t\t\t\tobj.text = text.replace(/(\\/n|\\\\n|\\n|\\r\\n)/g, "\\n");\n\t\t\t}else{\n\t\t\t\tif(obj.mode == 7){\n\t\t\t\t\ttry{\n\t\t\t\t\t\tadv = JSON.parse(format(text));\n\t\t\t\t\t\tobj.shadow = true;\n\t\t\t\t\t\tobj.x = parseFloat(adv[0]);\n\t\t\t\t\t\tobj.y = parseFloat(adv[1]);\n\t\t\t\t\t\tif(Math.floor(obj.x) < obj.x || Math.floor(obj.y) < obj.y){\n\t\t\t\t\t\t\tobj.position = "relative";\n\t\t\t\t\t\t}\n\t\t\t\t\t\tobj.text = adv[4].replace(/(\\/n|\\\\n|\\n|\\r\\n)/g, "\\n");\n\t\t\t\t\t\tobj.rZ = 0;\n\t\t\t\t\t\tobj.rY = 0;\n\t\t\t\t\t\tif(adv.length >= 7){\n\t\t\t\t\t\t\tobj.rZ = parseInt(adv[5], 10);\n\t\t\t\t\t\t\tobj.rY = parseInt(adv[6], 10);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tobj.motion = [];\n\t\t\t\t\t\tobj.movable = false;\n\t\t\t\t\t\tif(adv.length >= 11){\n\t\t\t\t\t\t\tobj.movable = true;\n\t\t\t\t\t\t\tvar singleStepDur = 500;\n\t\t\t\t\t\t\tvar motion = {\n\t\t\t\t\t\t\t\tx:{from: obj.x, to:parseFloat(adv[7]), dur:singleStepDur, delay:0},\n\t\t\t\t\t\t\t\ty:{from: obj.y, to:parseFloat(adv[8]), dur:singleStepDur, delay:0},\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tif(adv[9] !== \'\'){\n\t\t\t\t\t\t\t\tsingleStepDur = parseInt(adv[9], 10);\n\t\t\t\t\t\t\t\tmotion.x.dur = singleStepDur;\n\t\t\t\t\t\t\t\tmotion.y.dur = singleStepDur;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(adv[10] !== \'\'){\n\t\t\t\t\t\t\t\tmotion.x.delay = parseInt(adv[10], 10);\n\t\t\t\t\t\t\t\tmotion.y.delay = parseInt(adv[10], 10);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(adv.length > 11){\n\t\t\t\t\t\t\t\tobj.shadow = adv[11];\n\t\t\t\t\t\t\t\tif(obj.shadow === "true"){\n\t\t\t\t\t\t\t\t\tobj.shadow = true;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(obj.shadow === "false"){\n\t\t\t\t\t\t\t\t\tobj.shadow = false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(adv[12] != null){\n\t\t\t\t\t\t\t\t\tobj.font = adv[12];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif(adv.length > 14){\n\t\t\t\t\t\t\t\t\t// Support for Bilibili Advanced Paths\n\t\t\t\t\t\t\t\t\tif(obj.position === "relative"){\n\t\t\t\t\t\t\t\t\t\tconsole.log("Cannot mix relative and absolute positioning");\n\t\t\t\t\t\t\t\t\t\tobj.position = "absolute";\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tvar path = adv[14];\n\t\t\t\t\t\t\t\t\tvar lastPoint = {x:motion.x.from, y:motion.y.from};\n\t\t\t\t\t\t\t\t\tvar pathMotion = [];\n\t\t\t\t\t\t\t\t\tvar regex = new RegExp("([a-zA-Z])\\\\s*(\\\\d+)[, ](\\\\d+)","g");\n\t\t\t\t\t\t\t\t\tvar counts = path.split(/[a-zA-Z]/).length - 1;\n\t\t\t\t\t\t\t\t\tvar m = regex.exec(path);\n\t\t\t\t\t\t\t\t\twhile(m !== null){\n\t\t\t\t\t\t\t\t\t\tswitch(m[1]){\n\t\t\t\t\t\t\t\t\t\t\tcase "M":{\n\t\t\t\t\t\t\t\t\t\t\t\tlastPoint.x = parseInt(m[2],10);\n\t\t\t\t\t\t\t\t\t\t\t\tlastPoint.y = parseInt(m[3],10);\n\t\t\t\t\t\t\t\t\t\t\t}break;\n\t\t\t\t\t\t\t\t\t\t\tcase "L":{\n\t\t\t\t\t\t\t\t\t\t\t\tpathMotion.push({\n\t\t\t\t\t\t\t\t\t\t\t\t\t"x":{"from":lastPoint.x, "to":parseInt(m[2],10), "dur": singleStepDur / counts, "delay": 0},\n\t\t\t\t\t\t\t\t\t\t\t\t\t"y":{"from":lastPoint.y, "to":parseInt(m[3],10), "dur": singleStepDur / counts, "delay": 0}\n\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\tlastPoint.x = parseInt(m[2],10);\n\t\t\t\t\t\t\t\t\t\t\t\tlastPoint.y = parseInt(m[3],10);\n\t\t\t\t\t\t\t\t\t\t\t}break;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tm = regex.exec(path);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tmotion = null;\n\t\t\t\t\t\t\t\t\tobj.motion = pathMotion;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(motion !== null){\n\t\t\t\t\t\t\t\tobj.motion.push(motion);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tobj.dur = 2500;\n\t\t\t\t\t\tif(adv[3] < 12){\n\t\t\t\t\t\t\tobj.dur = adv[3] * 1000;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar tmp = adv[2].split(\'-\');\n\t\t\t\t\t\tif(tmp != null && tmp.length>1){\n\t\t\t\t\t\t\tvar alphaFrom = parseFloat(tmp[0]);\n\t\t\t\t\t\t\tvar alphaTo = parseFloat(tmp[1]);\n\t\t\t\t\t\t\tobj.opacity = alphaFrom;\n\t\t\t\t\t\t\tif(alphaFrom !== alphaTo){\n\t\t\t\t\t\t\t\tobj.alpha = {from:alphaFrom, to:alphaTo}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}catch(e){\n\t\t\t\t\t\tconsole.log(\'[Err] Error occurred in JSON parsing\');\n\t\t\t\t\t\tconsole.log(\'[Dbg] \' + text);\n\t\t\t\t\t}\n\t\t\t\t}else if(obj.mode == 8){\n\t\t\t\t\tobj.code = text; //Code comments are special\n\t\t\t\t}\n\t\t\t}\n\t\t\tif(obj.text != null)\n\t\t\t\tobj.text = obj.text.replace(/\\u25a0/g,"\\u2588");\n\t\t\ttlist.push(obj);\n\t\t}\n\t}\n\treturn tlist;\n}\n'),window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.AliplayerDanmuComponent=class{constructor(t){let A=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"controlbar";this.sendEl=A,this.danmukuList=t,this.html=(0,g.a8)('<div class="aliplayer-danmuku abp"> <div class="danmu container"></div> </div>'),this.danmuControlHtml=(0,g.a8)('<div class="ali-danmuku-control"> <div class="ali-danmu-input-wrap"></div> <i class="iconfont icon-danmu-close"></i> <div class="player-tooltip close"></div> <i class="iconfont icon-danmu-open" style="display:none"></i> <div class="player-tooltip open"></div> </div>'),this.sendEl=A,this.danmuInput=null===A?null:(0,g.a8)('<div class="ali-danmu-input"> <input type="text" placeholder=""> <button class="danmu-input-enter"></button> </div>'),this.CM=null,this.userDanmuOpen=!0}createEl(t,A){const e=A._options&&A._options.language;if(this.isEn=e&&"en-us"===e,null!==this.danmuInput&&(this.danmuInput.querySelector(".danmu-input-enter").innerText=this.isEn?"Enter":"发送",this.danmuInput.querySelector("input").setAttribute("placeholder",this.isEn?"Input danmu":"输入弹幕")),this.danmuControlHtml.querySelector(".player-tooltip.close").innerText=this.isEn?"Close Bullect":"关闭弹幕",this.danmuControlHtml.querySelector(".player-tooltip.open").innerText=this.isEn?"Open Bullect":"打开弹幕","controlbar"===this.sendEl){let t=this.danmuControlHtml.querySelector(".ali-danmu-input-wrap");t.style.display="inline-block",t.appendChild(this.danmuInput)}else if(null!==this.sendEl)if((0,g.vq)(this.sendEl))this.sendEl.appendChild(this.danmuInput);else{if("string"!=typeof this.sendEl)throw new Error("sendEl must be an element or selector string");{let t=document.querySelector(this.sendEl);if(!(0,g.vq)(t))throw new Error("sendEl must be an element or selector string");t.appendChild(this.danmuInput)}}t.querySelector(".prism-controlbar").appendChild(this.danmuControlHtml);let n=t.querySelector("video").nextElementSibling;n?t.insertBefore(this.html,n):t.appendChild(this.html),this.CM=new CommentManager(this.html.querySelector(".danmu")),this.CM.init(),this.CM.load(this.danmukuList),t.querySelector("video").ontimeupdate=()=>{null!==t.querySelector("video")&&this.CM.time(1e3*t.querySelector("video").currentTime)};let i=this.danmuControlHtml.querySelector(".icon-danmu-close"),o=this.danmuControlHtml.querySelector(".icon-danmu-open");if(i.onclick=()=>{this.userDanmuOpen=!1,i.style.display="none",o.style.display="inline-block",this.CM.clear(),this.CM.stop()},o.onclick=()=>{o.style.display="none",i.style.display="inline-block",this.userDanmuOpen=!0,this.CM.start()},null!==this.danmuInput){let t=this.danmuInput.querySelector(".ali-danmu-input input");this.danmuInput.querySelector(".danmu-input-enter").onclick=this.sendDanmuHandle.bind(this),t.onkeydown=t=>{13===t.keyCode&&this.sendDanmuHandle.call(this)}}}sendDanmuHandle(){let t=this.danmuInput.querySelector(".ali-danmu-input input"),A=t.value;""!==A&&(this.send({mode:1,text:A,stime:1e3,size:[16,18,25,36,45][this.randomIndex(5)],color:[16777215,255,13369344,16738047,16777011][this.randomIndex(5)]}),t.value="",t.focus())}randomIndex(t){return Math.floor(Math.random()*t)}play(t,A){this.userDanmuOpen&&this.CM.start()}pause(t,A){this.userDanmuOpen&&this.CM.stop()}send(t){this.CM.send(t)}insert(t){this.CM&&this.CM.insert(t)}dispose(){null!==this.danmuInput&&this.danmuInput.parentNode&&this.danmuInput.parentNode.removeChild(this.danmuInput)}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(2049),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.BulletScreenComponent=class{constructor(t,A){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"random";this.text=t,this.style=A||{fontSize:"14px",color:"#fff"},this.html=(0,u.a8)('<div class="bullet-screen paused"></div>'),this.bulletPosition=e}createEl(t,A){this.html.innerText=this.text,t.appendChild(this.html),t.style.overflow="hidden"}ready(t,A){console.log(t.getOptions()),!1===t.getOptions().autoplay&&(this.html.style.animationPlayState="paused"),Object.keys(this.style).forEach((t=>this.html.style[t]=this.style[t]));var e=this.html.offsetHeight,n=parseInt(getComputedStyle(t.tag).height.replace("px",""))-e;if("bottom"===this.bulletPosition)this.html.style.bottom=0;else{let t="top"===this.bulletPosition?0:this.randomTop(n);this.html.style.top=t}"random"===this.bulletPosition&&this.html.addEventListener("animationiteration",(()=>{this.html.style.top=this.randomTop(n)}))}playing(t,A){console.log("playering"),this.html.style.animationPlayState="running"}timeupdate(t,A){let e=t.el(),n=e.querySelector(".bullet-screen");if(n){"bullet-screen"!==n.className&&(n.className="bullet-screen");let t=getComputedStyle(n),A=t.getPropertyValue("display"),e=t.getPropertyValue("opacity"),i=t.getPropertyValue("visibility"),o=this.text,a=n.innerText;"none"===A&&n.style.setProperty("display","block"),"1"!==e&&n.style.setProperty("opacity","1"),"hidden"===i&&n.style.setProperty("visibility","visible"),o!=a&&(n.innerText=o)}else e.appendChild(this.html)}pause(t,A){console.log("pause"),this.html.style.animationPlayState="paused"}randomTop(t){return Math.floor(Math.random()*t)+"px"}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(1209),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.CaptionComponent=class{constructor(){this.captionList=null,this.html=(0,u.a8)('<div class="caption-components"> <div class="current-caption" data-ref=""></div> <ul class="caption-list"> </ul> </div>'),this.modalHtml=(0,u.a8)('<div class="caption-modal prism-info-display prism-info-left-bottom"> <span class="switchimg"></span> <span class="current-caption-tag"></span> </div>'),this.hasCreated=!1,this.definition=""}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.modalHtml.querySelector(".switchimg").innerText=this.isEn?"Switching to you for":"字幕切换到",this.modalHtml.querySelector(".switchimg").style.display="none",t.querySelector(".prism-controlbar").appendChild(this.html),t.appendChild(this.modalHtml),A.on("textTrackReady",(t=>{let{paramData:A}=t;this.captionList=A;let e=A.map((t=>`<li data-def="${t.value}">${t.text}</li>`));this.html.querySelector(".caption-list").innerHTML='<li style="background:rgba(88,87,86,.5);color:#fff">字幕</li>'+e.join("")}));let n=this.html.querySelector(".current-caption"),i=this.html.querySelector(".caption-list");0==this.hasCreated&&this.definition&&(i.querySelector(`li[data-def="${this.definition}"]`).className="current"),this.hasCreated=!0;let o=null;n.onclick=()=>{i.style.display="block"},n.onmouseleave=()=>{o=setTimeout((()=>{i.style.display="none"}),100)},i.onmouseenter=()=>{clearTimeout(o)},i.onmouseleave=()=>{i.style.display="none",this.modalHtml.style.display="none"},i.onclick=t=>{let{target:e}=t,n=e.dataset.def;if(n&&"current"!==e.className){let t=Array.isArray(this.captionList)?this.captionList.filter((t=>t.value===n))[0]:this.captionList.find((t=>t.value===n));A._ccService.switch(t.value),this.setCurrentCaption(t.text,t.value),this.modalHtml.style.display="block",this.modalHtml.querySelector(".switchimg").style.display="block",this.modalHtml.querySelector("span.current-caption-tag").innerText=t.text}}}setCurrentCaption(t,A){let e=this.html.querySelector(".current-caption");e.innerText=t,e.dataset.def=A,this.definition=A;let n=this.html.querySelector(".caption-list"),i=n.querySelector(".current");i&&(i.className="");let o=n.querySelectorAll("li");o.forEach((A=>{A.innerText===t&&(A.className="current")})),o&&(o.className="current")}created(t){}ready(t){this.modalHtml.style.display="none";let A=document.querySelector(".prism-setting-item.prism-setting-cc");A&&A.classList.add("player-hidden")}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(6730),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52),y=e(176);let g=class{constructor(t,A){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"关闭广告";this.adVideoSource=t,this.html=(0,u.a8)('<div class="many-video-ad-component"> <div class="videos"> </div> <a class="many-video-ad-link" target="_blank"></a> <div class="many-video-ad-close"> <span id="many-video-ad-duration"></span> <label> <span class="many-video-ad-close-text"></span> <i class="iconfont icon-close"></i> </label> </div> <a class="many-video-ad-detail" target="_blank">查看详情</a> <div class="autoplay-many-video-ad"> <i class="iconfont icon-player-play"></i> <span class="limit"></span> <span class="manual"></span> </div> </div>'),this.adInterval=null,this.adCloseFunction=A,this.html.querySelector(".many-video-ad-close-text").innerText=e,this.adDuration=null,this.player=null,this.indexVideo=1}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.html.querySelector(".many-video-ad-detail").innerText=this.isEn?"For more information":"查看广告详情",this.html.querySelector(".limit").innerText=this.isEn?"Your browser limits autoplay":"您的浏览器限制",this.html.querySelector(".manual").innerText=this.isEn?"Please Click":"自动播放请点击",t.appendChild(this.html)}created(t){let A=this.adVideoSource.map(((t,A)=>`<video id="many-video-ad-content${A+1}" style="${0===A?"display: block":"display:none"};width:100%;height:100%" x5-video-player-type="h5" x5-video-player-fullscreen="false" src="${t.adVideo}"></video>`));this.html.querySelector(".videos").innerHTML=A.join(" "),this.indexVideo=Number(this.indexVideo);let e=this.indexVideo,n=this.adVideoSource,i=this.html.querySelector("#many-video-ad-content"+this.indexVideo),o=this.html.querySelector(".many-video-ad-detail"),a=this.html.querySelector("#many-video-ad-duration"),r=this;var l=i.play();void 0!==l&&l.then((()=>{i.play()})).catch((t=>{document.querySelector(".autoplay-many-video-ad").style.display="block",i.oncanplay=()=>{let t=Math.ceil(i.duration);document.querySelector("#many-video-ad-duration").innerText=t}})),i.addEventListener("canplay",(function t(){i.removeEventListener("canplay",t),o.href=n[e-1].adVideoLink,i.play().then((()=>{let t=Math.ceil(i.duration);a.innerText=t,r.setAdInterval()})).catch((t=>{r.html.querySelector(".autoplay-many-video-ad").style.display="block",r.html.querySelector(".icon-player-play").onclick=()=>{r.playManyVideoAd(),r.html.querySelector(".autoplay-many-video-ad").style.display="none"}}))}))}ready(t,A){this.indexVideo=Number(this.indexVideo);let e=this.html.querySelector("#many-video-ad-content"+this.indexVideo),n=this;e.addEventListener("ended",(function(A){"-1"==n.playNext(n)&&t.play()})),this.html.querySelector(".many-video-ad-close label").onclick=()=>{"function"==typeof this.adCloseFunction?this.adCloseFunction(this):this.closeManyVideoAd()}}setAdInterval(t){let A=this.html.querySelector("#many-video-ad-duration"),e=this.html.querySelector("#many-video-ad-content"+this.indexVideo);this.adInterval=setInterval((()=>{let t=Math.ceil(e.duration)-Math.ceil(e.currentTime);A.innerText=t,1==t&&clearInterval(this.adInterval)}),1e3)}pauseManyVideoAd(){this.clearAdInterval(),this.html.querySelector("#many-video-ad-content"+this.indexVideo).pause()}playManyVideoAd(){this.setAdInterval(),this.html.querySelector("#many-video-ad-content"+this.indexVideo).play()}clearAdInterval(){null!==this.adInterval&&clearInterval(this.adInterval),this.adInterval=null}playNext(t){if(t.indexVideo>=t.adVideoSource.length)return t.html.parentNode.removeChild(t.html),-1;this.indexVideo=Number(this.indexVideo),null!=document.getElementById("many-video-ad-content"+this.indexVideo)&&document.getElementById("many-video-ad-content"+this.indexVideo).remove();let A=this.indexVideo+1;t.html.querySelector(".many-video-ad-detail").href=t.adVideoSource[t.indexVideo].adVideoLink;let e=this.html.querySelector("#many-video-ad-content"+A),n=this.html.querySelector("#many-video-ad-duration");e.style.display="block",this.adVideoSource.length>=this.indexVideo&&(this.indexVideo=this.indexVideo+1);let i=Math.ceil(e.duration);n.innerText=i,t.setAdInterval(),e.play(),e.addEventListener("ended",(function(A){"-1"==t.playNext(t)&&document.getElementById(player._options.id).getElementsByTagName("video")[0].play()}))}closeManyVideoAd(){this.clearAdInterval(),this.html.parentNode.removeChild(this.html),this.html=null,document.getElementById(player._options.id).getElementsByTagName("video")[0].play()}};y.A.mobile()&&(g=class{constructor(t,A){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"关闭广告";this.adVideoSource=t,this.html=(0,u.a8)('<div class="many-video-ad-component" style="background-color:transparent"> <div class="videosmb"></div> <a class="many-video-ad-link" target="_blank"></a> <div class="many-video-ad-close"> <span id="many-video-ad-duration"></span> <label> <span class="many-video-ad-close-text"></span> <i class="iconfont icon-close"></i> </label> </div> <a class="many-video-ad-detail" target="_blank"></a> <div class="autoplay-many-video-ad" style="display:block"> <i class="iconfont icon-player-play"></i> <span class="limit"></span> <span class="manual"></span> </div> </div>'),this.adInterval=null,this.adCloseFunction=A,this.html.querySelector(".many-video-ad-close-text").innerText=e,this.adDuration=null,this.player=null,this.indexVideo=1}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.html.querySelector(".many-video-ad-detail").innerText=this.isEn?"For more information":"查看广告详情",this.html.querySelector(".limit").innerText=this.isEn?"Your browser limits autoplay":"您的浏览器限制",this.html.querySelector(".manual").innerText=this.isEn?"Please Click":"自动播放请点击",t.appendChild(this.html),t.querySelector(".videosmb"),t.querySelector(".videosmb").setAttribute("preload","load");let n=t.querySelector(".prism-controlbar");n.className=n.className+" controlbar-element-hidden",this.html.querySelector(".icon-player-play").onclick=()=>{this.playManyVideoAd(),this.html.querySelector(".autoplay-many-video-ad").style.display="none"}}created(t){let A=this.adVideoSource.map(((t,A)=>`<video id="many-video-ad-content${A+1}" style="${0===A?"display: block":"display:none"};width:100%;height:100%" x5-video-player-type="h5" x5-video-player-fullscreen="false" src="${t.adVideo}"></video>`));this.html.querySelector(".videosmb").innerHTML=A.join(" "),this.indexVideo=Number(this.indexVideo);let e=this.indexVideo,n=this.adVideoSource,i=this.html.querySelector("#many-video-ad-content"+this.indexVideo),o=this.html.querySelector(".many-video-ad-detail"),a=this;i.addEventListener("canplay",(function t(){i.removeEventListener("canplay",t),o.href=n[e-1].adVideoLink,a.html.querySelector("#many-video-ad-duration").innerText=Math.ceil(i.duration)}))}ready(t){this.indexVideo=Number(this.indexVideo);let A=this.html.querySelector("#many-video-ad-content"+this.indexVideo),e=this;A.addEventListener("ended",(function(A){if("-1"==e.playNext(e)){t.play();let A=document.querySelector(".prism-controlbar");A.className=A.className.replace(" controlbar-element-hidden","")}})),this.html.querySelector(".many-video-ad-close label").onclick=()=>{"function"==typeof this.adCloseFunction?this.adCloseFunction(this):this.closeManyVideoAd()}}setAdInterval(){let t=this.html.querySelector("#many-video-ad-duration"),A=this.html.querySelector("#many-video-ad-content"+this.indexVideo);this.adInterval=setInterval((()=>{let e=Math.ceil(A.duration)-Math.ceil(A.currentTime);t.innerText=e,1==e&&clearInterval(this.adInterval)}),1e3)}closeManyVideoAd(){this.clearAdInterval();let t=this.html.parentNode.querySelector(".prism-controlbar");t.className=t.className.replace(" controlbar-element-hidden",""),this.html.parentNode.removeChild(this.html),this.html=null,document.getElementById(player._options.id).getElementsByTagName("video")[0].play()}clearAdInterval(){null!==this.adInterval&&clearInterval(this.adInterval),this.adInterval=null}playManyVideoAd(){this.setAdInterval(),this.html.querySelector("#many-video-ad-content"+this.indexVideo).play()}pauseManyVideoAd(){this.clearAdInterval(),this.html.querySelector("#many-video-ad-content"+this.indexVideo).pause()}playNext(t){if(t.indexVideo>=t.adVideoSource.length)return t.html.parentNode.removeChild(t.html),-1;this.indexVideo=Number(this.indexVideo),null!=document.getElementById("many-video-ad-content"+this.indexVideo)&&document.getElementById("many-video-ad-content"+this.indexVideo).remove();let A=this.indexVideo+1;t.html.querySelector(".many-video-ad-detail").href=t.adVideoSource[t.indexVideo].adVideoLink;let e=this.html.querySelector("#many-video-ad-content"+A),n=this.html.querySelector("#many-video-ad-duration");e.style.display="block",this.adVideoSource.length>=this.indexVideo&&(this.indexVideo=this.indexVideo+1);let i=Math.ceil(e.duration);n.innerText=i,t.setAdInterval(),e.play(),e.addEventListener("ended",(function(A){if("-1"==t.playNext(t)){document.getElementById(player._options.id).getElementsByTagName("video")[0].play();let t=document.querySelector(".prism-controlbar");t.className=t.className.replace(" controlbar-element-hidden","")}}))}});const M=g;window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.ManyVideoADComponent=M})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(4078),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.MemoryPlayComponent=class{constructor(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],A=arguments.length>1?arguments[1]:void 0,e=arguments.length>2?arguments[2]:void 0;this.html=(0,u.a8)('<div class="memory-play-wrap"></div>'),this.autoPlay=t,this.getTime=A||this._getTime,this.saveTimeFunction=e||this._saveTime,this.hasMemoryDisplay=!1}createEl(t){t.appendChild(this.html)}ready(t,A){let e=t.getOptions(),n=e.vid||e.source.replace(/\?.*$/,""),i=this.getTime(n);if(i=i?parseInt(i):0,null!==i&&0!==i&&!this.hasMemoryDisplay)if(this.hasMemoryDisplay=!0,this.autoPlay)t.seek(i),"playing"!==t.getStatus()&&t.play();else{let A=this.getVideoTime(i);if(i!==parseInt(t._duration)){let e=`<div class="memory-play">\n          <i class="iconfont icon-close"></i>\n          <span>上次看到</span>\n          <span>${A}</span>\n          <span class="play-jump">跳转播放</span>\n          </div>`;this.html.innerHTML=e;let n=setTimeout((()=>{this.html.innerHTML=""}),15e3);this.html.querySelector(".icon-close").onclick=()=>{this.html.innerHTML="",clearTimeout(n)},this.html.querySelector(".play-jump").onclick=()=>{t.seek(i),"playing"!==t.getStatus()&&t.play(),this.html.innerHTML="",clearTimeout(n)}}}document.onvisibilitychange=()=>{"hidden"===document.visibilityState&&0!==t.getCurrentTime()&&this.saveTimeFunction(n,t.getCurrentTime())},window.onbeforeunload=()=>{0!==t.getCurrentTime()&&this.saveTimeFunction(n,t.getCurrentTime())}}error(t,A){this.setMemory(t)}dispose(t,A){this.setMemory(t)}setMemory(t){let A=t.getOptions(),e=A.vid||A.source.replace(/\?.*$/,"");this.saveTimeFunction(e,t.getCurrentTime())}getVideoTime(t){let A=Math.round(t),e=Math.floor(A/3600),n=Math.floor((A-3600*e)/60),i=A-3600*e-60*n;return n<10&&(n="0"+n),i<10&&(i="0"+i),0===e?n+":"+i:e+":"+n+":"+i}_getTime(t){return localStorage.getItem(t)}_saveTime(t,A){localStorage.setItem(t,A)}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(2190),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.PauseADComponent=class{constructor(t,A){this.coverUrl=t,this.adUrl=A,this.html=(0,u.a8)('<div class="pause-ad"> <a class="btn-close"> <i class="split-left"></i> <i class="split-right"></i> <a> <span class="ad-text"></span> <a class="ad-content" target="_blank"> <img/> </a> </a></a></div>')}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.html.querySelector(".ad-text").innerText=this.isEn?"Ad":"广告";let n=this.html.querySelector(".ad-content"),i=n.querySelector("img");n.setAttribute("href",this.adUrl),i.setAttribute("src",this.coverUrl),this.html.querySelector(".btn-close").onclick=()=>{this.html.style.display="none"},t.appendChild(this.html)}play(t,A){this.html.style.display="none"}pause(t,A){this.html.style.display="block"}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(4843),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.PlaylistComponent=class{constructor(t){this.controlHtml=(0,u.a8)('<div class="playlist-component"> <i class="iconfont icon-skip-previous"></i> <div class="player-tooltip prev">上一个</div> <i class="iconfont icon-list"></i> <div class="player-tooltip list">播放列表</div> <i class="iconfont icon-skipnext"></i> <div class="player-tooltip next">下一个</div> </div>'),this.listHtml=(0,u.a8)('<div class="playlist-content"> <div class="list"></div> </div>'),this.playlist=t,this.playingVideoIndex=0,this.listHideTimeout=null}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.controlHtml.querySelector(".player-tooltip.prev").innerText=this.isEn?"Previous":"上一个",this.controlHtml.querySelector(".player-tooltip.list").innerText=this.isEn?"Playlist":"播放列表",this.controlHtml.querySelector(".player-tooltip.next").innerText=this.isEn?"Next":"下一个";let n=t.querySelector(".prism-controlbar"),i=n.querySelector(".prism-time-display");n.insertBefore(this.controlHtml,i),this.listHtml.onmouseleave=()=>{this.listHtml.style.width=0},this.listHtml.onmouseenter=this.clearHideListTimeout.bind(this),this.controlHtml.querySelector(".icon-list").onclick=this.tooglePlaylist.bind(this),this.listHtml.querySelector(".list").innerHTML=this.computedListDom(this.playlist);let o=A.getOptions()&&A.getOptions().source,a=0;o&&(a=this.playlist.findIndex((t=>t.source===o)),a=a>-1?a:0,this.playingVideoIndex=a>-1?a:0),this.listHtml.querySelector(".list").childNodes[0].className="video-item active",t.appendChild(this.listHtml)}ready(t,A){this.controlHtml.querySelector(".icon-skip-previous").onclick=()=>{t&&t.trigger("plugin-playlist-click-prev",{currentIndex:Math.max(this.playingVideoIndex-1,0)}),0!==this.playingVideoIndex?this.playVideo(t,this.playingVideoIndex-1):this.playlistTip(this.isEn?"Already the first one~":"已经是第一个了~",t._el)},this.controlHtml.querySelector(".icon-skipnext").onclick=()=>{t&&t.trigger("plugin-playlist-click-next",{currentIndex:Math.min(this.playingVideoIndex+1,this.playlist.length-1)}),this.playingVideoIndex!==this.playlist.length-1?this.playVideo(t,this.playingVideoIndex+1):this.playlistTip(this.isEn?"Already the last one~":"已经是最后一个了~",t._el)},this.listHtml.querySelector(".list").onclick=A=>{let e=A.target,n=parseInt(e.getAttribute("data-index"));t&&t.trigger("plugin-playlist-click-video",{currentIndex:this.playingVideoIndex,clickedIndex:n}),"video-item"===e.className&&this.playVideo(t,n)}}clearHideListTimeout(){null!==this.listHideTimeout&&(clearTimeout(this.listHideTimeout),this.listHideTimeout=null)}playVideo(t,A){this.playingVideoIndex!==A&&(t&&t.trigger("plugin-playlist-change",{currentIndex:A}),this.playingVideoIndex=parseInt(A),t.loadByUrl(this.playlist[A].source),this.listHtml.querySelector(".video-item.active").className="video-item",this.listHtml.querySelector(".list").childNodes[A].className="video-item active")}tooglePlaylist(){this.clearHideListTimeout(),"30%"===this.listHtml.style.width?this.listHtml.style.width=0:(this.listHtml.style.width="30%",this.listHideTimeout=setTimeout((()=>{this.listHtml.style.width=0}),5e3))}playlistTip(t,A){let e=document.createElement("div");e.className="playlist-skip-tip",e.innerText=t,A.appendChild(e),setTimeout((function(){A.removeChild(e)}),3e3)}computedListDom(t){return t.map(((t,A)=>`<div class="video-item" data-index="${A}" title="${t.name}">${t.name}</div>`)).join("")}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(9918),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.PreviewVodComponent=class{constructor(t){let A=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this.previewDuration=t,this.html=(0,u.a8)('<div class="preview-vod-component"> <div class="preview-component-layer"> <div class="preview-custom"> <p class="preview-default"></p> </div> </div> <div class="preview-component-tip"> <span class="can-preview"></span><span class="preview-time"></span>， <span class="preview-custom-bar">Become VIP Watch Full Version </span> <span class="preview-vod-close">x</span> </div> </div>'),null!==A&&this.insertHTtml(A,"previewEndHtml"),null!==e&&this.insertHTtml(e,"previewBarHtml")}play(t){this.previewEnd&&(t._el.querySelector(".center").classList.remove("preview-hide"),t.seek(0),this.previewEnd=!1)}insertHTtml(t,A){let e="previewEndHtml"===A?".preview-custom":".preview-custom-bar",n=this.html.querySelector(e);if("string"==typeof t)if("#"===t[0]){let e=document.querySelector(t);e?n.innerHTML=e.innerHTML:console.warn(`[aliplayer components warn]: Invalid parameter ${A}, can't find element by this id`)}else n.innerHTML=t;else console.warn(`[aliplayer components warn]: Invalid parameter ${A}, ${A} must be a string type`)}ready(t){let A=parseInt(t.getDuration());this.videoDuration=A,!this.invalidPreview&&this.previewDuration>=A&&(this.invalidPreview=!0,console.warn("[aliplayer components warn]: Invalid parameter previewDuration, previewDuration must be less than the video duration!")),0!==this.previewDuration&&this.previewDuration<A&&(this.html.style.display="block")}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,console.log(this.html.querySelector(".preview-default"));let n=this.html.querySelector(".preview-default");n&&(n.innerText=this.isEn?"Preview is over":"试看已结束"),this.html.querySelector(".can-preview").innerText=this.isEn?"Try":"可试看";let i=this.previewDuration;0===i&&(this.html.style.display="none");let o=i/60,a=o.toString().split(".")[1];o=a&&a.length>1?" "+i+(this.isEn?" senconds":" 秒"):" "+o+(this.isEn?" minutes":" 分钟"),i<60&&(o=" "+i+(this.isEn?" senconds":" 秒")),this.html.querySelector(".preview-time").innerText=o;let r=t.querySelector("video").nextElementSibling;r?t.insertBefore(this.html,r):t.appendChild(this.html),this.html.querySelector(".preview-vod-close").addEventListener("click",(()=>{this.html.querySelector(".preview-component-tip").style.display="none"})),A.setPreviewTime(Number(this.previewDuration))}closePreviewLayer(){this.previewEnd=!1,this.html.querySelector(".preview-component-layer").style.display="none"}timeupdate(t){if(!this.previewEnd&&0!==this.previewDuration&&this.previewDuration<this.videoDuration){let A=t.getPreviewTime(),e=t.getCurrentTime();Math.floor(A)<e&&(this.previewEnd=!0,t._el.querySelector(".center")&&t._el.querySelector(".center").classList.add("preview-hide"),t.seek(A),t.pause(),this.html.querySelector(".preview-component-layer").style.display="block")}}ended(t,A){t.isPreview()&&(this.html.querySelector(".preview-component-layer").style.display="block")}}})(),(()=>{"use strict";var t=e(52),A=e(5072),n=e.n(A),i=e(7825),o=e.n(i),a=e(7659),r=e.n(a),l=e(5056),s=e.n(l),c=e(540),d=e.n(c),p=e(1113),m=e.n(p),h=e(2008),u={};u.styleTagTransform=m(),u.setAttributes=s(),u.insert=r().bind(null,"head"),u.domAPI=o(),u.insertStyleElement=d(),n()(h.A,u),h.A&&h.A.locals&&h.A.locals,window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.ProgressComponent=class{constructor(){this.html=(0,t.a8)('<div class="progress-component"> <div class="progress-content clearfix"> <div class="img-wrap"><img/></div> <div class="info"> <div class="time"></div> <div class="describe"></div> </div> <i href="" target="_blank" class="iconfont icon-play pregress-play-btn"></i> </div> <i class="iconfont icon-arrowdown"></i> </div>'),this.imgEle=this.html.querySelector(".img-wrap img"),this.timeEle=this.html.querySelector(".time"),this.playBtnEle=this.html.querySelector(".pregress-play-btn"),this.describeEle=this.html.querySelector(".describe"),this.timer=null,this.currentOffset=null}createEl(t,A){t.appendChild(this.html),this.html.onmouseenter=()=>{null!==this.timer&&(clearTimeout(this.timer),this.timer=null)},this.html.onmouseleave=()=>{this.html.style.display="none"},this.html.onclick=()=>{this.html.style.display="none"},this.html.querySelector(".pregress-play-btn").addEventListener("click",(()=>{A.seek(this.currentOffset)}))}markerDotOver(t,A){let e=t._el.clientWidth,n=`calc(${100*A.left}% - 10px)`;e*A.left+323>e?(n=e-330+"px",this.html.querySelector(".icon-arrowdown").style.left=e*A.left-e+317+"px"):this.html.querySelector(".icon-arrowdown").style.left="-2px";let{coverUrl:i,title:o,describe:a,offset:r}=A.progressMarker;this.currentOffset=r,this.html.style.left=n,this.imgEle.src=i,this.timeEle.innerText=o,this.describeEle.innerText=a,this.html.style.display="block"}markerDotOut(t,A){this.timer=setTimeout((()=>{this.html.style.display="none"}),100)}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(4200),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.QualityComponent=class{constructor(t){this.html=(0,u.a8)('<div class="quality-components"> <div class="current-quality" data-ref=""></div> <ul class="quality-list"> </ul> </div>'),this.modalHtml=(0,u.a8)('<div class="quality-modal"> <span class="switchimg"></span> <span class="current-quality-tag"></span> , <span class="wait"></span> </div>'),this.hasCreated=!1,this.definition="",this.getQuality=t,this._levels=[]}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.html.querySelector(".current-quality").innerText=this.isEn?"Resolution":"清晰度",this.modalHtml.querySelector(".switchimg").innerText=this.isEn?"Switching to you for":"正在为您切换到",this.modalHtml.querySelector(".wait").innerText=this.isEn?"Please wait...":"请稍候...",t.querySelector(".prism-controlbar").appendChild(this.html),t.appendChild(this.modalHtml)}setCurrentQuality(t,A){let e=this.html.querySelector(".current-quality");e.innerText=t||"",e.dataset.def=A,this.definition=A;let n=this.html.querySelector(".quality-list"),i=n.querySelector(".current");i&&(i.className="");let o=n.querySelector(`li[data-def="${A}"]`);o&&(o.className="current")}created(t){this._urls=t._urls;let A=this.html.querySelector(".current-quality"),e=this.html.querySelector(".quality-list");e.style.display="none",t.on("settingListHide",(()=>{e.style.display="none"})),t.on("selectorUpdateList",(n=>{if("quality"!==n.paramData.type)return;if(!t.getOptions().isVBR)return;let i=t._qualityService.levels;this._levels=(i||[]).map((t=>({...t,definition:t.bitrate||"AUTO"})));let o=this._levels.map((t=>`<li data-def="${t.definition}">${t.desc}</li>`));e.innerHTML=o.join(""),A.style.width="100px",e.style.width="100px";const a=(Array.isArray(this._levels)?this._levels.filter((t=>t.definition===this.definition))[0]:this._levels.find((t=>t.definition===this.definition)))?.desc;this.setCurrentQuality(a,this.definition)}));let n=this._urls.map((t=>`<li data-def="${t.definition}">${t.desc}</li>`));e.innerHTML=n.join(""),0==this.hasCreated&&this.definition&&(e.querySelector(`li[data-def="${this.definition}"]`).className="current"),this.hasCreated=!0;let i=null;A.onclick=()=>{const t="none"!==e.style.display;e.style.display=t?"none":"block"},A.onmouseleave=()=>{i&&clearTimeout(i),i=setTimeout((()=>{e.style.display="none"}),150)},e.onmouseenter=()=>{clearTimeout(i)},e.onmouseleave=()=>{i&&clearTimeout(i),i=setTimeout((()=>{e.style.display="none"}),150)},e.onclick=A=>{let{target:e}=A,n=e.dataset.def,i=e.innerText;if(n&&"current"!==e.className){let A=Array.isArray(this._levels?.length>0?this._levels:this._urls)?(this._levels?.length>0?this._levels:this._urls).filter((t=>String(t.definition)===n))[0]:(this._levels?.length>0?this._levels:this._urls).find((t=>String(t.definition)===n));if(A){if((0,u.DR)("selectedStreamLevel",A.definition,365),t._switchLevel&&!t._options.isLive&&t._options.isVBR){let e=Array.isArray(this._levels)?this._levels.filter((t=>Number(t.bitrate)===Number(n)))[0]:this._levels.find((t=>Number(t.bitrate)===Number(n)));t._switchLevel(A.Url,e)}else t._loadByUrlInner(A.Url,t.getCurrentTime(),!0);this.setCurrentQuality(A.desc,A.definition),this.modalHtml.style.display="block",this.modalHtml.querySelector("span.current-quality-tag").innerText=A.desc,setTimeout((()=>{this.modalHtml.style.display="none"}),2e3)}}"function"==typeof this.getQuality&&this.getQuality(n,i)}}ready(t){this.modalHtml.style.display="none";let A=document.querySelector(".prism-setting-item.prism-setting-quality");A&&A.classList.add("player-hidden")}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(6175),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.RateComponent=class{constructor(){this.html=(0,u.a8)('<div class="rate-components"> <div class="current-rate">1.0x</div> <ul class="rate-list"> <li data-rate="2.0">2.0x</li> <li data-rate="1.5">1.5x</li> <li data-rate="1.25">1.25x</li> <li data-rate="1.0" class="current">1.0x</li> <li data-rate="0.5">0.5x</li> </ul> </div>')}createEl(t){t.querySelector(".prism-controlbar").appendChild(this.html)}ready(t,A){let e=this.html.querySelector(".current-rate"),n=this.html.querySelector(".rate-list"),i=null,o=document.querySelector(".prism-setting-item.prism-setting-speed");o&&o.classList.add("player-hidden"),e.onclick=()=>{n.style.display="block"},e.onmouseleave=()=>{i=setTimeout((()=>{n.style.display="none"}),100)},n.onmouseenter=()=>{clearTimeout(i)},n.onmouseleave=()=>{n.style.display="none"},n.onclick=A=>{let{target:i}=A,o=i.dataset.rate;if(o){if(t.setSpeed(o),"current"!==i.className){let t=n.querySelector(".current");t&&(t.className=""),i.className="current"}n.style.display="none",e.innerText=o+"x"}}}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(6481),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.RotateMirrorComponent=class{constructor(){this.html=(0,u.a8)('<div class="aliplayer-rotate-mirror"> <i class="iconfont icon-player-rotate-left"></i> <div class="player-tooltip counterclockwise"></div> <i class="iconfont icon-player-rotate-right"></i> <div class="player-tooltip clockwise"></div> <i class="iconfont icon-player-switch"></i> <div class="player-tooltip switch"></div> <div class="mirror-option" style="display:none"> <div class="mirror-item" data-id="vertical"></div> <div class="mirror-item" data-id="horizon"></div> <div class="mirror-item" data-id="counterclockwise"></div> <div class="mirror-item" data-id="clockwise"></div> </div> </div>')}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.html.querySelector(".player-tooltip.counterclockwise").innerText=this.isEn?"Rotate 45 degrees counterclockwise":"逆时针旋转45度",this.html.querySelector('.mirror-item[data-id="counterclockwise"]').innerText=this.isEn?"Rotate left 45 ̊":"左旋转45˚",this.html.querySelector(".player-tooltip.clockwise").innerText=this.isEn?"Rotate 45 degrees clockwise":"顺时针旋转45度",this.html.querySelector('.mirror-item[data-id="clockwise"]').innerText=this.isEn?"Rotate right 45 ̊":"右旋转45˚",this.html.querySelector(".player-tooltip.switch").innerText=this.isEn?"Mirror":"镜像",this.html.querySelector(".mirror-item[data-id=vertical]").innerText=this.isEn?"Vertical mirroring":"垂直镜像",this.html.querySelector(".mirror-item[data-id=horizon]").innerText=this.isEn?"Horizontal mirroring":"水平镜像",t.querySelector(".prism-controlbar").appendChild(this.html)}ready(t,A){this.html.querySelector(".icon-player-rotate-left").onclick=function(){let A=t.getRotate();t.setRotate(A-45)},this.html.querySelector(".icon-player-rotate-right").onclick=function(){let A=t.getRotate();t.setRotate(A+45)};let e=this.html.querySelector(".mirror-option");this.html.querySelector(".icon-player-switch").onclick=()=>{let t=e.style.display;e.style.display="none"===t?"block":"none"},e.onmouseleave=function(){this.style.display="none"},e.onclick=function(A){let e=A.target;if("counterclockwise"!==e.dataset.id)if("clockwise"!==e.dataset.id)if(e.className.match("active"))t.cancelImage(),e.className="mirror-item";else{let A="horizon"===e.dataset.id?e.previousElementSibling:e.nextElementSibling;A.className.match("active")&&(A.className="mirror-item",t.cancelImage());let n=e.getAttribute("data-id");t.setImage(n),e.className="mirror-item active"}else{let A=t.getRotate();t.setRotate(A+45)}else{let A=t.getRotate();t.setRotate(A-45)}}}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(2484),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.StartADComponent=class{constructor(t,A,e){if(this.coverUrl=t,this.adUrl=A,this.adDuration=e,e<=0)throw Error("adDuration must must be greater than 0");this.html=(0,u.a8)('<div class="start-ad"> <span class="tip"><span class="ad-name">广告</span>: <i></i><span class="second">秒</span></span> <a class="ad-content" target="_blank"> <img/> </a> </div>')}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.html.querySelector(".ad-name").innerText=this.isEn?"Ad":"广告",this.html.querySelector(".second").innerText=this.isEn?"s":"秒";let n=this.html.querySelector(".ad-content");if(n.setAttribute("href",this.adUrl),n.querySelector("img").setAttribute("src",this.coverUrl),this.html.querySelector(".tip i").innerText=this.adDuration,t.appendChild(this.html),null!==this.html){"init"!==A.getStatus()&&A.pause();let t=this.adDuration,e=this.html.querySelector(".tip i"),n=setInterval((()=>{if(t-=1,e.innerText=t,0===t){if(clearInterval(n),1==A.__disposed)return;this.removeComponent();let t=A.getOptions(),e=t.vid||t.source.replace(/\?.*$/,""),i=localStorage.getItem(e)||0;i&&(i=parseInt(i));let o=A.getOptions()&&A.getOptions().components,a=Array.isArray(o)?o.filter((t=>"MemoryPlayComponent"===t.type.name))[0]:o.find((t=>"MemoryPlayComponent"===t.type.name));(!a||!a.args[0]||A.getCurrentTime()>=i)&&A.play()}}),1e3)}}removeComponent(){this.html.parentNode?.removeChild(this.html),this.html=null}ready(t){null!==this.html&&t.pause()}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(9218),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52);window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.TrackComponent=class{constructor(){this.trackList=null,this.html=(0,u.a8)('<div class="track-components"> <div class="current-track"></div> <ul class="track-list"> </ul> </div>'),this.modalHtml=(0,u.a8)('<div class="track-modal prism-info-display prism-info-left-bottom"> <span class="switchimg"></span> <span class="current-track-tag"></span> </div>'),this.hasCreated=!1,this.definition=""}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.modalHtml.querySelector(".switchimg").innerText=this.isEn?"Track to you for":"音轨切换到",this.modalHtml.querySelector(".switchimg").style.display="none",t.querySelector(".prism-controlbar").appendChild(this.html),t.appendChild(this.modalHtml),A.on("audioTrackReady",(t=>{let{paramData:e}=t;this.trackList=e;let n=e.map((t=>`<li data-def="${t.value}">${t.text}</li>`));this.html.querySelector(".track-list").innerHTML='<li style="background:rgba(88,87,86,.5);color:#fff">音轨</li>'+n.join("");let i=A._audioTrackService._defaultTrack;i||(i=this.trackList[0]),this.setCurrentTrack(i.text,i.value)}));let n=this.html.querySelector(".current-track"),i=this.html.querySelector(".track-list");0==this.hasCreated&&this.definition&&(i.querySelector(`li[data-def="${this.definition}"]`).className="current"),this.hasCreated=!0;let o=null;n.onclick=()=>{i.style.display="block"},n.onmouseleave=()=>{o=setTimeout((()=>{i.style.display="none"}),100)},i.onmouseenter=()=>{clearTimeout(o)},i.onmouseleave=()=>{i.style.display="none",this.modalHtml.style.display="none"},i.onclick=t=>{let{target:e}=t,n=e.dataset.def;if(n&&"current"!==e.className){let t=Array.isArray(this.trackList)?this.trackList.filter((t=>t.value.toString()===n))[0]:this.trackList.find((t=>t.value.toString()===n));A._audioTrackService.switch(t.value),this.setCurrentTrack(t.text,t.value),this.modalHtml.style.display="block",this.modalHtml.querySelector(".switchimg").style.display="block",this.modalHtml.querySelector("span.current-track-tag").innerText=t.text}}}setCurrentTrack(t,A){let e=this.html.querySelector(".current-track");e.innerText=t,e.dataset.def=A,this.definition=A;let n=this.html.querySelector(".track-list"),i=n.querySelector(".current");i&&(i.className="");let o=n.querySelectorAll("li");o.forEach((A=>{A.innerText===t&&(A.className="current")})),o&&(o.className="current")}created(t){}ready(t){this.modalHtml.style.display="none";let A=document.querySelector(".prism-setting-item.prism-setting-audio");A&&A.classList.add("player-hidden")}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(6613),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals;var u=e(52),y=e(176);let g=class{constructor(t,A,e){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"关闭广告";this.adVideoSource=t,this.adLink=A,this.html=(0,u.a8)('<div class="video-ad-component"> <video id="video-ad-content" x5-video-player-type="h5" x5-video-player-fullscreen="false"></video> <a class="video-ad-link" target="_blank"></a> <div class="video-ad-close"> <span id="video-ad-duration"></span> <label> <span class="video-ad-close-text"></span> <i class="iconfont icon-close"></i> </label> </div> <a class="video-ad-detail" target="_blank"></a> <div class="autoplay-video-ad"> <i class="iconfont icon-player-play"></i> <span class="limit"></span> <span class="manual"></span> </div> <div id="loadflag" class="prism-loading loading-center"> <div class="circle"></div> <div class="circle1"></div> </div> </div>'),this.adInterval=null,this.adCloseFunction=e,this.html.querySelector(".video-ad-close-text").innerText=n,this.adDuration=null,this.player=null}createEl(t,A){this.player=A;const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.html.querySelector(".video-ad-detail").innerText=this.isEn?"For more information":"查看广告详情",this.html.querySelector(".limit").innerText=this.isEn?"Your browser limits autoplay":"您的浏览器限制",this.html.querySelector(".manual").innerText=this.isEn?"Please Click":"自动播放请点击";let n=this.html.querySelector("#video-ad-content");n.setAttribute("src",this.adVideoSource);let i=this;this.html.querySelector(".icon-player-play").onclick=()=>{this.playVideoAd(),this.html.querySelector(".autoplay-video-ad").style.display="none"},n.addEventListener("canplay",(function t(){n.removeEventListener("canplay",t),document.getElementById("loadflag").style.display="none",i.adDuration=Math.ceil(n.duration),i.html.querySelector("#video-ad-duration").innerText=i.adDuration,n.play().then((()=>{i.setAdInterval()})).catch((t=>{i.html.querySelector(".autoplay-video-ad").style.display="block"}))}));let o=this.html.querySelector(".video-ad-link"),a=this.html.querySelector(".video-ad-detail");o.setAttribute("href",this.adLink),a.setAttribute("href",this.adLink),t.appendChild(this.html),this.html.querySelector(".video-ad-close label").onclick=()=>{"function"==typeof this.adCloseFunction?this.adCloseFunction(this):this.closeVideoAd()}}ready(t,A){null!==this.html&&(t.pause(),this.player=t)}pauseVideoAd(){this.clearAdInterval(),this.html.querySelector("#video-ad-content").pause()}playVideoAd(){this.setAdInterval(),this.html.querySelector("#video-ad-content").play()}clearAdInterval(){null!==this.adInterval&&clearInterval(this.adInterval),this.adInterval=null}setAdInterval(){let t=this.html.querySelector("#video-ad-duration");this.adInterval=setInterval((()=>{this.adDuration-=1,this.adDuration<=0?this.closeVideoAd():t.innerText=this.adDuration}),1e3)}closeVideoAd(){this.clearAdInterval(),this.html.parentNode.removeChild(this.html),this.html=null,this.player&&this.player.getOptions().autoplay&&this.player.play()}};y.A.mobile()&&(g=class{constructor(t,A,e){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"关闭广告";this.adVideoSource=t,this.adLink=A,this.html=(0,u.a8)('<div class="video-ad-component" style="background-color:transparent"> <a class="video-ad-link" target="_blank"></a> <div class="video-ad-close"> <span id="video-ad-duration"></span> <label> <span class="video-ad-close-text"></span> <i class="iconfont icon-close"></i> </label> </div> <a class="video-ad-detail" target="_blank"></a> <div class="autoplay-video-ad" style="display:block"> <i class="iconfont icon-player-play"></i> <span class="limit"></span> <span class="manual"></span> </div> </div>'),this.adInterval=null,this.adCloseFunction=e,this.html.querySelector(".video-ad-close-text").innerText=n,this.html.querySelector(".video-ad-link").setAttribute("href",this.adLink),this.html.querySelector(".video-ad-detail").setAttribute("href",this.adLink),this.adDuration=null}createEl(t,A){const e=A._options&&A._options.language;this.isEn=e&&"en-us"===e,this.html.querySelector(".video-ad-detail").innerText=this.isEn?"For more information":"查看广告详情",this.html.querySelector(".limit").innerText=this.isEn?"Your browser limits autoplay":"您的浏览器限制",this.html.querySelector(".manual").innerText=this.isEn?"Please Click":"自动播放请点击",t.appendChild(this.html),t.querySelector("video"),t.querySelector("video").setAttribute("preload","load");let n=t.querySelector(".prism-controlbar");n.className=n.className+" controlbar-element-hidden";let i=t.querySelector(".prism-big-play-btn");i.className=i.className+" controlbar-element-hidden"}created(t){if(this.player=t,this.vdSource=t.getOptions().source,t.loadByUrl(this.adVideoSource),this.html.querySelector(".autoplay-video-ad").onclick=()=>{null!==this.adDuration&&(t.loadByUrl(this.adVideoSource),this.html.parentNode.querySelector(".prism-big-play-btn").click())},null===this.adDuration){this.adDuration=void 0;let A=this.html.parentNode.querySelector("video"),e=this;function n(){let i=A.duration;isNaN(i)||0===i||(A.removeEventListener("timeupdate",n),e.adDuration=Math.ceil(A.duration),"none"!==e.html.querySelector(".autoplay-video-ad").style.display&&(e.html.querySelector(".autoplay-video-ad").style.display="none",t.play()),e.html.querySelector("#video-ad-duration").innerText=e.adDuration,e.setAdInterval())}A.addEventListener("timeupdate",n)}}ready(t){this.html.querySelector(".video-ad-close label").onclick=()=>{"function"==typeof this.adCloseFunction?this.adCloseFunction(this):this.closeVideoAd(),document.getElementById(t.id()).getElementsByTagName("video")[0].play()}}setAdInterval(){let t=this.html.querySelector("#video-ad-duration");this.adInterval=setInterval((()=>{this.adDuration-=1,this.adDuration<=0?(this.closeVideoAd(),document.getElementById(this.player.id()).getElementsByTagName("video")[0].play()):t.innerText=this.adDuration}),1e3)}closeVideoAd(){this.clearAdInterval(),this.player.loadByUrl(this.vdSource);let t=this.html.parentNode.querySelector(".prism-controlbar");t.className=t.className.replace(" controlbar-element-hidden","");let A=this.html.parentNode.querySelector(".prism-big-play-btn");A.className=A.className.replace(" controlbar-element-hidden",""),this.player.getOptions().autoplay&&this.player.play(),this.html.parentNode.removeChild(this.html)}clearAdInterval(){null!==this.adInterval&&clearInterval(this.adInterval),this.adInterval=null}playVideoAd(){this.setAdInterval(),this.player.play()}pauseVideoAd(){this.clearAdInterval(),this.player.pause()}});const M=g;window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.VideoADComponent=M})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(5570),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals,window.AliPlayerComponent||(window.AliPlayerComponent={}),window.AliPlayerComponent.PlayerNextComponent=class{constructor(t){this.clickHandle=t,this._html=document.createElement("div"),this._html.setAttribute("class","player-olympic-player-next");let A=document.createElement("div");A.setAttribute("class","player-olympic-player-next-tip"),A.textContent="Next",this._html.appendChild(A)}createEl(t){this._html.addEventListener("click",this.clickHandle),t.querySelector(".prism-play-btn").insertAdjacentElement("afterend",this._html)}}})(),(()=>{"use strict";var t=e(5072),A=e.n(t),n=e(7825),i=e.n(n),o=e(7659),a=e.n(o),r=e(5056),l=e.n(r),s=e(540),c=e.n(s),d=e(1113),p=e.n(d),m=e(807),h={};h.styleTagTransform=p(),h.setAttributes=l(),h.insert=a().bind(null,"head"),h.domAPI=i(),h.insertStyleElement=c(),A()(m.A,h),m.A&&m.A.locals&&m.A.locals})()})();