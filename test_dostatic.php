<?php
/**
 * DoStatic 命令测试脚本
 * 用于验证 DoStatic 命令的基本功能
 */

// 引入 ThinkPHP 框架
require_once __DIR__ . '/thinkphp/base.php';

use think\Console;
use think\Db;

echo "=== DoStatic 命令测试脚本 ===\n\n";

// 1. 检查数据库连接
echo "1. 检查数据库连接...\n";
try {
    $dbConfig = config('database');
    echo "数据库配置: " . $dbConfig['hostname'] . ":" . $dbConfig['hostport'] . "/" . $dbConfig['database'] . "\n";
    
    // 测试连接
    $result = Db::query("SELECT 1");
    echo "✓ 数据库连接正常\n\n";
} catch (Exception $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "\n\n";
    exit(1);
}

// 2. 检查必要的表是否存在
echo "2. 检查数据表...\n";
$tables = [
    'org_user_watch' => '观看记录表',
    'org_agency_tube' => '群管表',
    'org_tube_static' => '统计数据表'
];

foreach ($tables as $table => $desc) {
    try {
        $exists = Db::query("SHOW TABLES LIKE 'ksd_{$table}'");
        if ($exists) {
            echo "✓ {$desc} (ksd_{$table}) 存在\n";
        } else {
            echo "✗ {$desc} (ksd_{$table}) 不存在\n";
        }
    } catch (Exception $e) {
        echo "✗ 检查表 {$table} 时出错: " . $e->getMessage() . "\n";
    }
}
echo "\n";

// 3. 检查配置文件
echo "3. 检查配置文件...\n";
try {
    $staticConfig = config('static');
    if ($staticConfig) {
        echo "✓ static.php 配置文件加载成功\n";
        echo "  - 统计表名: " . $staticConfig['static_config']['table_name'] . "\n";
        echo "  - 数据保留天数: " . $staticConfig['static_config']['retention_days'] . "\n";
    } else {
        echo "✗ static.php 配置文件加载失败\n";
    }
} catch (Exception $e) {
    echo "✗ 配置文件检查失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. 检查命令注册
echo "4. 检查命令注册...\n";
try {
    $commands = config('command');
    if (isset($commands['dostatic'])) {
        echo "✓ dostatic 命令已注册\n";
        echo "  - 命令类: " . $commands['dostatic'] . "\n";
    } else {
        echo "✗ dostatic 命令未注册\n";
    }
} catch (Exception $e) {
    echo "✗ 命令注册检查失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. 检查样本数据
echo "5. 检查样本数据...\n";
try {
    // 检查群管数据
    $tubeCount = Db::name('org_agency_tube')->where('status', 1)->count();
    echo "✓ 活跃群管数量: {$tubeCount}\n";
    
    // 检查观看记录数据
    $watchCount = Db::name('org_user_watch')->count();
    echo "✓ 观看记录总数: {$watchCount}\n";
    
    // 检查今日观看记录
    $todayStart = strtotime(date('Y-m-d 00:00:00'));
    $todayEnd = strtotime(date('Y-m-d 23:59:59'));
    $todayCount = Db::name('org_user_watch')
        ->where('create_time', 'between', [$todayStart, $todayEnd])
        ->count();
    echo "✓ 今日观看记录: {$todayCount}\n";
    
} catch (Exception $e) {
    echo "✗ 样本数据检查失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. 测试命令执行（干运行）
echo "6. 测试命令类加载...\n";
try {
    $commandClass = 'app\\admin\\command\\DoStatic';
    if (class_exists($commandClass)) {
        echo "✓ DoStatic 命令类加载成功\n";
        
        // 检查必要的方法
        $methods = ['configure', 'execute'];
        $reflection = new ReflectionClass($commandClass);
        
        foreach ($methods as $method) {
            if ($reflection->hasMethod($method)) {
                echo "✓ 方法 {$method} 存在\n";
            } else {
                echo "✗ 方法 {$method} 不存在\n";
            }
        }
    } else {
        echo "✗ DoStatic 命令类加载失败\n";
    }
} catch (Exception $e) {
    echo "✗ 命令类检查失败: " . $e->getMessage() . "\n";
}
echo "\n";

// 7. 提供测试建议
echo "7. 测试建议:\n";
echo "如果以上检查都通过，可以尝试以下命令进行实际测试:\n\n";
echo "# 查看命令帮助\n";
echo "php think dostatic --help\n\n";
echo "# 测试统计今天的数据\n";
echo "php think dostatic\n\n";
echo "# 测试统计指定日期的数据\n";
echo "php think dostatic -d " . date('Y-m-d') . "\n\n";
echo "# 测试清理功能\n";
echo "php think dostatic -c\n\n";

echo "=== 测试完成 ===\n";
