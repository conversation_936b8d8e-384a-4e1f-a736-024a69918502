<?xml version="1.0" encoding="UTF-8"?>
<files psalm-version="5.26.1@d747f6500b38ac4f7dfc5edbcae6e4b637d7add0">
  <file src="src/FnStream.php">
    <InvalidFunctionCall>
      <code><![CDATA[($this->_fn___toString)()]]></code>
      <code><![CDATA[($this->_fn_close)()]]></code>
      <code><![CDATA[($this->_fn_detach)()]]></code>
      <code><![CDATA[($this->_fn_eof)()]]></code>
      <code><![CDATA[($this->_fn_getContents)()]]></code>
      <code><![CDATA[($this->_fn_getMetadata)($key)]]></code>
      <code><![CDATA[($this->_fn_getSize)()]]></code>
      <code><![CDATA[($this->_fn_isReadable)()]]></code>
      <code><![CDATA[($this->_fn_isSeekable)()]]></code>
      <code><![CDATA[($this->_fn_isWritable)()]]></code>
      <code><![CDATA[($this->_fn_read)($length)]]></code>
      <code><![CDATA[($this->_fn_rewind)()]]></code>
      <code><![CDATA[($this->_fn_seek)($offset, $whence)]]></code>
      <code><![CDATA[($this->_fn_tell)()]]></code>
      <code><![CDATA[($this->_fn_write)($string)]]></code>
    </InvalidFunctionCall>
  </file>
  <file src="src/Header.php">
    <PossiblyUndefinedArrayOffset>
      <code><![CDATA[$m[0]]]></code>
    </PossiblyUndefinedArrayOffset>
  </file>
  <file src="src/HttpFactory.php">
    <ParamNameMismatch>
      <code><![CDATA[$file]]></code>
    </ParamNameMismatch>
  </file>
  <file src="src/Message.php">
    <PossiblyNullArgument>
      <code><![CDATA[$matches[1] === '/' ? self::parseRequestUri($parts[1], $data['headers']) : $parts[1]]]></code>
      <code><![CDATA[$parts[1]]]></code>
      <code><![CDATA[$parts[1]]]></code>
    </PossiblyNullArgument>
    <PossiblyUndefinedArrayOffset>
      <code><![CDATA[$parts[1]]]></code>
      <code><![CDATA[$parts[1]]]></code>
      <code><![CDATA[$parts[1]]]></code>
      <code><![CDATA[$parts[1]]]></code>
    </PossiblyUndefinedArrayOffset>
  </file>
  <file src="src/MessageTrait.php">
    <LessSpecificImplementedReturnType>
      <code><![CDATA[MessageInterface]]></code>
      <code><![CDATA[MessageInterface]]></code>
      <code><![CDATA[MessageInterface]]></code>
      <code><![CDATA[MessageInterface]]></code>
      <code><![CDATA[MessageInterface]]></code>
      <code><![CDATA[array]]></code>
      <code><![CDATA[array]]></code>
    </LessSpecificImplementedReturnType>
    <ParamNameMismatch>
      <code><![CDATA[$header]]></code>
      <code><![CDATA[$header]]></code>
      <code><![CDATA[$header]]></code>
      <code><![CDATA[$header]]></code>
      <code><![CDATA[$header]]></code>
      <code><![CDATA[$header]]></code>
    </ParamNameMismatch>
  </file>
  <file src="src/Request.php">
    <NoValue>
      <code><![CDATA[$header]]></code>
    </NoValue>
  </file>
  <file src="src/Response.php">
    <RedundantCast>
      <code><![CDATA[(int) $code]]></code>
      <code><![CDATA[(string) $reasonPhrase]]></code>
    </RedundantCast>
  </file>
  <file src="src/ServerRequest.php">
    <InvalidArgument>
      <code><![CDATA[$_SERVER['SERVER_PORT']]]></code>
    </InvalidArgument>
    <InvalidArrayOffset>
      <code><![CDATA[$normalizedFiles[$key]]]></code>
    </InvalidArrayOffset>
    <InvalidReturnStatement>
      <code><![CDATA[$normalizedFiles]]></code>
    </InvalidReturnStatement>
    <InvalidReturnType>
      <code><![CDATA[UploadedFileInterface[]]]></code>
    </InvalidReturnType>
    <ParamNameMismatch>
      <code><![CDATA[$attribute]]></code>
      <code><![CDATA[$attribute]]></code>
      <code><![CDATA[$attribute]]></code>
    </ParamNameMismatch>
  </file>
  <file src="src/Stream.php">
    <InvalidPropertyAssignmentValue>
      <code><![CDATA[$this->stream]]></code>
    </InvalidPropertyAssignmentValue>
    <RedundantCast>
      <code><![CDATA[(int) $whence]]></code>
    </RedundantCast>
    <RedundantPropertyInitializationCheck>
      <code><![CDATA[isset($this->stream)]]></code>
      <code><![CDATA[isset($this->stream)]]></code>
      <code><![CDATA[isset($this->stream)]]></code>
      <code><![CDATA[isset($this->stream)]]></code>
      <code><![CDATA[isset($this->stream)]]></code>
      <code><![CDATA[isset($this->stream)]]></code>
      <code><![CDATA[isset($this->stream)]]></code>
      <code><![CDATA[isset($this->stream)]]></code>
      <code><![CDATA[isset($this->stream)]]></code>
      <code><![CDATA[isset($this->stream)]]></code>
    </RedundantPropertyInitializationCheck>
  </file>
  <file src="src/Uri.php">
    <PossiblyInvalidArgument>
      <code><![CDATA[$result]]></code>
    </PossiblyInvalidArgument>
  </file>
  <file src="src/UriResolver.php">
    <TypeDoesNotContainType>
      <code><![CDATA['' === $relativePath]]></code>
    </TypeDoesNotContainType>
  </file>
  <file src="src/Utils.php">
    <FalsableReturnStatement>
      <code><![CDATA[$contents]]></code>
    </FalsableReturnStatement>
    <MissingDocblockType>
      <code><![CDATA[throw $ex;]]></code>
      <code><![CDATA[throw $ex;]]></code>
    </MissingDocblockType>
    <PossiblyUndefinedVariable>
      <code><![CDATA[$contents]]></code>
      <code><![CDATA[$handle]]></code>
    </PossiblyUndefinedVariable>
  </file>
</files>
