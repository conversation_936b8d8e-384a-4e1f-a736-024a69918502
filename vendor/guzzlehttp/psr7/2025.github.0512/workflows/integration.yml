name: Integration

on:
  push:
    branches:
  pull_request:

permissions:
  contents: read

jobs:
  build:
    name: Test
    runs-on: ubuntu-22.04
    strategy:
      max-parallel: 10
      matrix:
        php: ['7.2', '7.3', '7.4', '8.0', '8.1', '8.2', '8.3', '8.4']

    steps:
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          ini-values: error_reporting=E_ALL
          coverage: none

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: composer update --no-interaction --no-progress

      - name: Start server
        run: php -S 127.0.0.1:10002 tests/Integration/server.php &

      - name: Run Guzzle tests
        env:
          TEST_SERVER: 127.0.0.1:10002
        run: vendor/bin/phpunit --testsuite "Guzzle PSR-7 Integration Test Suite"

      - name: Run Interop tests
        env:
          TEST_SERVER: 127.0.0.1:10002
        run: vendor/bin/phpunit --testsuite "PSR-17 Integration Test Suite"
