<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    backupGlobals="true"
    colors="true"
    beStrictAboutOutputDuringTests="true"
    beStrictAboutTestsThatDoNotTestAnything="true"
    bootstrap="vendor/autoload.php"
    convertDeprecationsToExceptions="true"
>
    <testsuites>
        <testsuite name="Guzzle PSR-7 Unit Test Suite">
            <directory>tests</directory>
            <exclude>tests/Integration</exclude>
        </testsuite>
        <testsuite name="Guzzle PSR-7 Integration Test Suite">
            <directory>tests/Integration</directory>
        </testsuite>
        <testsuite name="PSR-17 Integration Test Suite">
            <directory>./vendor/http-interop/http-factory-tests/test</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist>
            <directory>src</directory>
        </whitelist>
    </filter>
    <php>
        <const name="REQUEST_FACTORY" value="GuzzleHttp\Psr7\HttpFactory"/>
        <const name="RESPONSE_FACTORY" value="GuzzleHttp\Psr7\HttpFactory"/>
        <const name="SERVER_REQUEST_FACTORY" value="GuzzleHttp\Psr7\HttpFactory"/>
        <const name="STREAM_FACTORY" value="GuzzleHttp\Psr7\HttpFactory"/>
        <const name="UPLOADED_FILE_FACTORY" value="GuzzleHttp\Psr7\HttpFactory"/>
        <const name="URI_FACTORY" value="GuzzleHttp\Psr7\HttpFactory"/>
    </php>
</phpunit>
