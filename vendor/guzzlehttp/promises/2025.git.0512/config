[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = https://github.com/guzzle/promises.git
	fetch = +refs/heads/*:refs/remotes/origin/*
	pushurl = **************:guzzle/promises.git
[branch "2.2"]
	remote = origin
	merge = refs/heads/2.2
[remote "composer"]
	url = https://github.com/guzzle/promises.git
	fetch = +refs/heads/*:refs/remotes/composer/*
