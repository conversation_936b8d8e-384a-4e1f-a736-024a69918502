{"name": "alibabacloud/openapi-core", "description": "Alibaba Cloud OpenApi Client Core", "type": "library", "license": "Apache-2.0", "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "require": {"php": ">5.5", "alibabacloud/credentials": "^1.2.2", "alibabacloud/darabonba": "^1", "alibabacloud/gateway-spi": "^1"}, "require-dev": {"symfony/dotenv": "^3.4", "phpunit/phpunit": "^4.8.35|^5.4.3|^9.3", "symfony/var-dumper": "^3.4"}, "autoload": {"psr-4": {"Darabonba\\OpenApi\\": "src"}}, "scripts": {"fixer": "php-cs-fixer fix ./"}, "config": {"sort-packages": true, "preferred-install": "dist", "optimize-autoloader": true}, "prefer-stable": true, "github": "https://github.com/aliyun/darabonba-openapi", "main": "src/OpenApiClient.php"}