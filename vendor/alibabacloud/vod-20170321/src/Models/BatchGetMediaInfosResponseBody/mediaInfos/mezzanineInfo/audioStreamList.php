<?php

// This file is auto-generated, don't edit it. Thanks.

namespace AlibabaCloud\SDK\Vod\V20170321\Models\BatchGetMediaInfosResponseBody\mediaInfos\mezzanineInfo;

use AlibabaCloud\Dara\Model;

class audioStreamList extends Model
{
    /**
     * @var string
     */
    public $bitrate;

    /**
     * @var string
     */
    public $channelLayout;

    /**
     * @var string
     */
    public $channels;

    /**
     * @var string
     */
    public $codecLongName;

    /**
     * @var string
     */
    public $codecName;

    /**
     * @var string
     */
    public $codecTag;

    /**
     * @var string
     */
    public $codecTagString;

    /**
     * @var string
     */
    public $codecTimeBase;

    /**
     * @var string
     */
    public $duration;

    /**
     * @var string
     */
    public $index;

    /**
     * @var string
     */
    public $lang;

    /**
     * @var string
     */
    public $numFrames;

    /**
     * @var string
     */
    public $sampleFmt;

    /**
     * @var string
     */
    public $sampleRate;

    /**
     * @var string
     */
    public $startTime;

    /**
     * @var string
     */
    public $timebase;
    protected $_name = [
        'bitrate' => 'Bitrate',
        'channelLayout' => 'ChannelLayout',
        'channels' => 'Channels',
        'codecLongName' => 'CodecLongName',
        'codecName' => 'CodecName',
        'codecTag' => 'CodecTag',
        'codecTagString' => 'CodecTagString',
        'codecTimeBase' => 'CodecTimeBase',
        'duration' => 'Duration',
        'index' => 'Index',
        'lang' => 'Lang',
        'numFrames' => 'NumFrames',
        'sampleFmt' => 'SampleFmt',
        'sampleRate' => 'SampleRate',
        'startTime' => 'StartTime',
        'timebase' => 'Timebase',
    ];

    public function validate()
    {
        parent::validate();
    }

    public function toArray($noStream = false)
    {
        $res = [];
        if (null !== $this->bitrate) {
            $res['Bitrate'] = $this->bitrate;
        }

        if (null !== $this->channelLayout) {
            $res['ChannelLayout'] = $this->channelLayout;
        }

        if (null !== $this->channels) {
            $res['Channels'] = $this->channels;
        }

        if (null !== $this->codecLongName) {
            $res['CodecLongName'] = $this->codecLongName;
        }

        if (null !== $this->codecName) {
            $res['CodecName'] = $this->codecName;
        }

        if (null !== $this->codecTag) {
            $res['CodecTag'] = $this->codecTag;
        }

        if (null !== $this->codecTagString) {
            $res['CodecTagString'] = $this->codecTagString;
        }

        if (null !== $this->codecTimeBase) {
            $res['CodecTimeBase'] = $this->codecTimeBase;
        }

        if (null !== $this->duration) {
            $res['Duration'] = $this->duration;
        }

        if (null !== $this->index) {
            $res['Index'] = $this->index;
        }

        if (null !== $this->lang) {
            $res['Lang'] = $this->lang;
        }

        if (null !== $this->numFrames) {
            $res['NumFrames'] = $this->numFrames;
        }

        if (null !== $this->sampleFmt) {
            $res['SampleFmt'] = $this->sampleFmt;
        }

        if (null !== $this->sampleRate) {
            $res['SampleRate'] = $this->sampleRate;
        }

        if (null !== $this->startTime) {
            $res['StartTime'] = $this->startTime;
        }

        if (null !== $this->timebase) {
            $res['Timebase'] = $this->timebase;
        }

        return $res;
    }

    public function toMap($noStream = false)
    {
        return $this->toArray($noStream);
    }

    public static function fromMap($map = [])
    {
        $model = new self();
        if (isset($map['Bitrate'])) {
            $model->bitrate = $map['Bitrate'];
        }

        if (isset($map['ChannelLayout'])) {
            $model->channelLayout = $map['ChannelLayout'];
        }

        if (isset($map['Channels'])) {
            $model->channels = $map['Channels'];
        }

        if (isset($map['CodecLongName'])) {
            $model->codecLongName = $map['CodecLongName'];
        }

        if (isset($map['CodecName'])) {
            $model->codecName = $map['CodecName'];
        }

        if (isset($map['CodecTag'])) {
            $model->codecTag = $map['CodecTag'];
        }

        if (isset($map['CodecTagString'])) {
            $model->codecTagString = $map['CodecTagString'];
        }

        if (isset($map['CodecTimeBase'])) {
            $model->codecTimeBase = $map['CodecTimeBase'];
        }

        if (isset($map['Duration'])) {
            $model->duration = $map['Duration'];
        }

        if (isset($map['Index'])) {
            $model->index = $map['Index'];
        }

        if (isset($map['Lang'])) {
            $model->lang = $map['Lang'];
        }

        if (isset($map['NumFrames'])) {
            $model->numFrames = $map['NumFrames'];
        }

        if (isset($map['SampleFmt'])) {
            $model->sampleFmt = $map['SampleFmt'];
        }

        if (isset($map['SampleRate'])) {
            $model->sampleRate = $map['SampleRate'];
        }

        if (isset($map['StartTime'])) {
            $model->startTime = $map['StartTime'];
        }

        if (isset($map['Timebase'])) {
            $model->timebase = $map['Timebase'];
        }

        return $model;
    }
}
