2025-04-23 Version: 3.7.4
- Update API GetJobDetail: add response parameters Body.WorkflowTaskDetail.


2025-04-17 Version: 3.7.3
- Update API SubmitWorkflowJob: add response parameters Body.TaskId.


2025-03-21 Version: 3.7.2
- Generated php 2017-03-21 for vod.

2025-03-21 Version: 3.7.2
- Generated php 2017-03-21 for vod.

2025-03-21 Version: 3.7.1
- Generated php 2017-03-21 for vod.

2025-03-21 Version: 3.7.0
- Support API GetDailyPlayRegionStatis.


2025-03-20 Version: 3.6.0
- Support API DescribeVodEditingUsageData.
- Update API DescribeVodTieringStorageData: add param AppId.
- Update API DescribeVodTieringStorageRetrievalData: add param AppId.
- Update API GetVideoList: update response param.


2025-01-08 Version: 3.5.1
- Update API BatchGetMediaInfos: update response param.


2024-12-19 Version: 3.5.0
- Support API BatchGetMediaInfos.
- Support API DescribeMediaDistribution.
- Update API DescribeVodDomainUsageData: update param DomainName.
- Update API UploadStreamByURL: add param UploadMetadata.


2024-12-17 Version: 3.5.0
- Support API DescribeMediaDistribution.
- Update API DescribeVodDomainUsageData: update param DomainName.


2024-12-09 Version: 3.4.1
- Update API GetPlayInfo: update response param.


2024-12-06 Version: 3.4.0
- Support API GetJobDetail.
- Support API ListJobInfo.
- Update API GetTranscodeTask: add param JobIds.
- Update API GetTranscodeTask: update param TranscodeTaskId.
- Update API GetTranscodeTask: update response param.


2024-11-29 Version: 3.3.3
- Generated php 2017-03-21 for vod.

2024-11-28 Version: 3.3.2
- Update API SubmitTranscodeJobs: add param SessionId.


2024-11-26 Version: 3.3.1
- Update API GetVideoInfo: update response param.
- Update API GetVideoInfos: update response param.
- Update API UpdateVideoInfo: add param UserData.


2024-10-11 Version: 3.3.0
- Support API ChangeResourceGroup.
- Update API GetPlayInfo: update response param.


2024-08-21 Version: 3.2.0
- Support API DescribeVodSSLCertificateList.
- Support API SetVodDomainSSLCertificate.
- Update API DescribeVodDomainCertificateInfo: update response param.


2024-08-20 Version: 3.1.0
- Support API DescribeVodDomainMax95BpsData.


2024-08-13 Version: 3.0.1
- Update API CreateAppInfo: add param ResourceGroupId.
- Update API DescribeVodCertificateList: update response param.
- Update API GetAppInfos: update response param.
- Update API GetMezzanineInfo: update response param.
- Update API ListAppInfo: add param ResourceGroupId.
- Update API ListAppInfo: update response param.
- Update API SetMessageCallback: update param CallbackURL.
- Update API SubmitAIMediaAuditJob: update param TemplateId.


2024-07-05 Version: 3.0.0
- Support API DescribeVodDomainQpsData.
- Support API DescribeVodTieringStorageData.
- Support API DescribeVodTieringStorageRetrievalData.
- Update API DescribeVodDomainDetail: update response param.
- Update API DescribeVodDomainRealTimeReqHitRateData: update response param.
- Update API DescribeVodDomainReqHitRateData: update response param.
- Update API DescribeVodMediaPlayData: update response param.
- Update API DescribeVodStorageData: add param AppId.
- Update API GetVideoInfo: update response param.
- Update API GetVideoInfos: update response param.
- Update API PreloadVodObjectCaches: add param Area.
- Update API PreloadVodObjectCaches: add param L2Preload.
- Update API PreloadVodObjectCaches: add param WithHeader.
- Update API UpdateWatermark: update param Name.


2024-04-25 Version: 2.22.0
- Support API DescribeVodDomainRealTimeDetailData.


2024-04-25 Version: 2.21.0
- Support API DescribeVodDomainBpsDataByLayer.
- Support API DescribeVodDomainHitRateData.
- Support API DescribeVodDomainRealTimeBpsData.
- Support API DescribeVodDomainRealTimeByteHitRateData.
- Support API DescribeVodDomainRealTimeHttpCodeData.
- Support API DescribeVodDomainRealTimeQpsData.
- Support API DescribeVodDomainRealTimeReqHitRateData.
- Support API DescribeVodDomainRealTimeTrafficData.
- Support API DescribeVodDomainReqHitRateData.
- Support API DescribeVodRangeDataByLocateAndIspService.


2024-04-11 Version: 2.20.0
- Support API DescribeVodMediaPlayData.
- Update API DeleteAttachedMedia: update param MediaIds.
- Update API DescribeVodStorageData: update param Storage.
- Update API DescribeVodStorageData: update param StorageType.
- Update API DescribeVodTranscodeData: update param Specification.
- Update API DescribeVodTranscodeData: update param Storage.
- Update API RefreshVodObjectCaches: add param Force.
- Update API RestoreMedia: update param MediaIds.


2024-04-11 Version: 2.20.0
- Support API DescribeVodMediaPlayData.
- Update API DeleteAttachedMedia: update param MediaIds.
- Update API DescribeVodStorageData: update param Storage.
- Update API DescribeVodStorageData: update param StorageType.
- Update API DescribeVodTranscodeData: update param Specification.
- Update API DescribeVodTranscodeData: update param Storage.
- Update API RefreshVodObjectCaches: add param Force.
- Update API RestoreMedia: update param MediaIds.


2024-03-26 Version: 2.19.2
- Update API DescribeVodDomainUsageData: update response param.
- Update API DescribeVodTranscodeData: add param AppId.


2024-01-05 Version: 2.19.1
- Generated php 2017-03-21 for vod.

2024-01-04 Version: 2.19.0
- Generated php 2017-03-21 for vod.

2023-12-15 Version: 2.18.1
- Generated php 2017-03-21 for vod.

2023-12-13 Version: 2.18.0
- Generated php 2017-03-21 for vod.

2023-11-03 Version: 2.17.1
- Generated php 2017-03-21 for vod.

2023-10-18 Version: 2.17.0
- Generated php 2017-03-21 for vod.

2023-08-19 Version: 2.16.15
- Generated php 2017-03-21 for vod.

2023-07-28 Version: 2.16.14
- Latest version for php.

2023-04-26 Version: 2.16.13
- Api add media storage class.

2022-12-23 Version: 2.16.12
- Support resourcegroup.

2022-10-27 Version: 2.16.11
- Modify DescribeVodUserDomains api.
- Modify CreateUploadImage api.

2022-08-25 Version: 2.16.10
- Add kms apis.

2022-04-11 Version: 2.16.9
- Set multiple apis to public.

2022-02-11 Version: 2.16.8
- Add media entity APIs.

2021-12-22 Version: 2.16.6
- Add return fields to UploadStreamByURL.
- Set DescribeVodDomainSrcTrafficData pulic.

2021-12-20 Version: 2.16.5
- Add return fields to UploadStreamByURL.

2021-11-24 Version: 2.16.4
- Set API DescribeVodDomainSrcBpsData to public.

2021-11-16 Version: 2.16.3
- Add parameter type HDRType.

2021-11-02 Version: 2.16.0
- Add API UploadStreamByURL.

2021-09-16 Version: 2.15.13
- Add copyright invasion detection APIs.

2021-03-08 Version: 2.0.1
- Generated php 2017-03-21 for vod.

2020-12-30 Version: 2.0.0
- AMP Version Change.

2020-12-30 Version: 2.0.0
- AMP Version Change.

