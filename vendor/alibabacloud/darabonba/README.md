
![](https://aliyunsdk-pages.alicdn.com/icons/AlibabaCloud.svg)

## Alibaba Cloud Tea for Java

[![CI](https://github.com/aliyun/tea-php/actions/workflows/ci.yml/badge.svg)](https://github.com/aliyun/tea-php/actions/workflows/ci.yml)
[![codecov](https://codecov.io/gh/aliyun/tea-php/branch/master/graph/badge.svg)](https://codecov.io/gh/aliyun/tea-php)
[![Latest Stable Version](https://poser.pugx.org/alibabacloud/tea/v/stable)](https://packagist.org/packages/alibabacloud/tea)
[![License](https://poser.pugx.org/alibabacloud/tea/license)](https://packagist.org/packages/alibabacloud/tea)

## Installation

```sh
composer require alibabacloud/tea --optimize-autoloader
```

> Some users may not be able to install due to network problems, you can try to switch the Composer mirror.

## Changelog

Detailed changes for each release are documented in the [release notes](CHANGELOG.md).

## License

[Apache-2.0](LICENSE.md)

Copyright (c) 2009-present, Alibaba Cloud All rights reserved.
