[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = https://github.com/aliyun/credentials-php.git
	fetch = +refs/heads/*:refs/remotes/origin/*
	pushurl = **************:aliyun/credentials-php.git
[branch "master"]
	remote = origin
	merge = refs/heads/master
[remote "composer"]
	url = https://github.com/aliyun/credentials-php.git
	fetch = +refs/heads/*:refs/remotes/composer/*
