<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="tests/bootstrap.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false"
         logIncompleteSkipped="true"
         testSuiteLoaderFile="phpunit/src/Runner/StandardTestSuiteLoader.php">

    <testsuites>
        <testsuite name="All">
            <directory>tests</directory>
        </testsuite>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>

        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>

    <groups>
        <exclude>
            <group>integration</group>
        </exclude>
    </groups>

    <logging>
        <log type="coverage-html" target="cache/coverage" lowUpperBound="35" highLowerBound="70"/>
        <log type="coverage-clover" target="cache/coverage.clover"/>
    </logging>

    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./src</directory>
            <exclude>
                <file>./src/Credential/Config.php</file>
                <file>./src/Credential/CredentialModel.php</file>
                <file>./src/Providers/CredentialsProvider.php</file>
                <file>./src/CredentialsInterface.php</file>
                <file>./src/CredentialsProviderWrap.php</file>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
