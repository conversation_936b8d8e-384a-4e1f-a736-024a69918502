name: PHP CI

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
defaults:
  run:
    shell: bash

permissions:
  id-token: write

jobs:
  build:

    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: [ubuntu-latest]
        php-versions: ['5.6', '7.0', '7.1', '7.2', '7.3', '7.4', '8.0', '8.1', '8.2']
      fail-fast: false
    name: PHP ${{ matrix.php-versions }} Test on ${{ matrix.operating-system }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}
        extensions: mbstring, intl
        ini-values: post_max_size=256M, max_execution_time=180
        coverage: xdebug
        tools: php-cs-fixer, phpunit
    
    - name: Validate composer.json and composer.lock
      run: composer validate

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-suggest
    
    - name: Setup OIDC
      run: npm install @actions/core@1.6.0 @actions/http-client

    - name: Get Id Token
      uses: actions/github-script@v7
      id: idtoken
      with:
        script: |
          const coreDemo = require('@actions/core');
          const idToken = await coreDemo.getIDToken('sts.aliyuncs.com');
          const fsx = require('fs/promises');
          await fsx.writeFile('/tmp/oidc_token', idToken);

    - name: Run test case
      run: composer test
      env:
        ACCESS_KEY_ID: ${{ secrets.ACCESS_KEY_ID }}
        ACCESS_KEY_SECRET: ${{ secrets.ACCESS_KEY_SECRET }}
        ROLE_ARN: ${{ secrets.ROLE_ARN }}
        PUBLIC_KEY_ID: ${{ secrets.PUBLIC_KEY_ID }}
        PRIVATE_KEY_LINE_1: ${{ secrets.PRIVATE_KEY_LINE_1 }}

        # for OIDC
        ALIBABA_CLOUD_OIDC_PROVIDER_ARN: ${{ secrets.ALIBABA_CLOUD_OIDC_PROVIDER_ARN }}
        ALIBABA_CLOUD_OIDC_TOKEN_FILE: "/tmp/oidc_token"
        ALIBABA_CLOUD_ROLE_ARN: ${{ secrets.OIDC_ROLE_ARN }}
    
    - name: Upload Coverage Report
      uses: codecov/codecov-action@v4
      with:
        token: ${{ secrets.CODECOV_TOKEN }} # required
